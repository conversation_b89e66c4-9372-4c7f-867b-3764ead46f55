import {authenticate} from '@loopback/authentication';
import {UserRepository} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {post, requestBody, response} from '@loopback/rest';
import {UserProfile as LibUserProfile, SecurityBindings} from '@loopback/security';
import {getRPTextFormat} from '../helper/filter-assignment-year-helper';
import {
  FormCollectionRepository,
  NewMetricRepository,
  UserProfileRepository,
  UserRoleAuthorizationRepository
} from '../repositories';
import {UserProfileController} from './user-profile.controller';

// Error code definitions for STT Report validation
interface SttReportError {
  category: string;
  httpCode: number;
  appErrorCode: number;
  errorName: string;
  description: string;
}

const STT_REPORT_ERRORS = {
  // Authentication & Authorization Errors
  UNAUTHORIZED_ERROR: {
    category: 'Authentication & Authorization',
    httpCode: 401,
    appErrorCode: 2000,
    errorName: 'UnauthorizedError',
    description: 'User is not authenticated'
  },
  FORBIDDEN_ERROR: {
    category: 'Authentication & Authorization',
    httpCode: 403,
    appErrorCode: 2001,
    errorName: 'ForbiddenError',
    description: 'Authenticated but lacks permission'
  },
  TOKEN_EXPIRED_ERROR: {
    category: 'Authentication & Authorization',
    httpCode: 401,
    appErrorCode: 2002,
    errorName: 'TokenExpiredError',
    description: 'Session token has expired'
  },
  INVALID_TOKEN_ERROR: {
    category: 'Authentication & Authorization',
    httpCode: 401,
    appErrorCode: 2003,
    errorName: 'InvalidTokenError',
    description: 'Token malformed or invalid'
  },
  // Validation Errors
  VALIDATION_ERROR: {
    category: 'Validation Errors',
    httpCode: 400,
    appErrorCode: 1000,
    errorName: 'ValidationError',
    description: 'Basic Validation Failure'
  },
  MISSING_FIELD_ERROR: {
    category: 'Validation Errors',
    httpCode: 400,
    appErrorCode: 1001,
    errorName: 'MissingFieldError',
    description: 'Required field is missing'
  },
  INVALID_FORMAT_ERROR: {
    category: 'Validation Errors',
    httpCode: 400,
    appErrorCode: 1002,
    errorName: 'InvalidFormatError',
    description: 'Field format is incorrect (e.g., date/email)'
  },
  VALUE_OUT_OF_RANGE_ERROR: {
    category: 'Validation Errors',
    httpCode: 400,
    appErrorCode: 1003,
    errorName: 'ValueOutOfRangeError',
    description: 'Input value exceeds allowed range'
  },
  DEPENDENCY_VALIDATION_ERROR: {
    category: 'Validation Errors',
    httpCode: 400,
    appErrorCode: 1004,
    errorName: 'DependencyValidationError',
    description: 'Field value is invalid due to another field'
  }
} as const;

interface DcfAssignment {
  dcfId: string;
  locationId: number;
  level: number;
  start_date: string;
  end_date: string | null;
  frequency: number;
  reportingPeriods?: ReportingPeriod;
}

interface ReportingPeriod {
  valid_periods: string[];
  data_granularity: string;
  start_month?: string;
  end_month?: string;
}

interface ReportingFrequencyInput {
  reporting_frequency: string;
  reporting_period_from?: string;
  reporting_period_to?: string;
  reporting_period: string;
  year: string | number | number[];
  entity: string[];
}

const frequencyMapping: {[key: number]: string} = {
  1: 'monthly',
  2: 'bi-monthly',
  3: 'quarterly',
  4: 'yearly',
  5: 'half-yearly'
};

interface FrequencyWeight {
  [key: string]: number;
  monthly: number;
  'bi-monthly': number;
  quarterly: number;
  'half-yearly': number;
  yearly: number;
  custom: number;
}

export class SttReportController {
  constructor(
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @repository(UserRepository)
    public userRepository: UserRepository
  ) { }
  private hardcodedSTTIndicatorList = [
    {
      id: "CI1",
      title: "GHG Scope 1 & 2 Market-based",
      type: 1, unit: 'tCO2e',
      dcfIds: [304, 305, 287],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "Sum of GHG scope 1 [tCO2e] and GHG scope 2 market-based [tCO2e]"
    },
    {
      id: "CI2",
      title: "GHG Scope 1",
      type: 1, unit: 'tCO2e',
      dcfIds: [304, 305],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "Emissions from refrigerant [tCO2e] and Emissions from fuel consumption [tCO2e]"
    },
    {
      id: "CI5",
      title: "GHG Scope 2 Location-based",
      type: 1, unit: 'tCO2e',
      dcfIds: [287],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "(Grid electricity consumption [MWh] + renewable energy from PPA [MWh] + renewable energy from green tariff [MWh]) x Grid emission factor [tCO2e/energy unit]"
    },


    {
      id: "CI10",
      title: "Total Non-hazardous Waste Generated",
      type: 1, unit: 'tonnes',
      dcfIds: [307],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "sum of non-hazardous waste disposed [tonnes]"
    },
    {
      id: "CI11",
      title: "Non-hazardous Waste Diversion",
      type: 1, unit: 'tonnes',
      dcfIds: [307],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "sum of non-hazardous waste disposed through recycling, recovery, composting,... [tonnes]"
    },
    {
      id: "CI12",
      title: "Total Hazardous Waste Generated",
      type: 1, unit: 'tonnes',
      dcfIds: [297], allowedType: [],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "sum of hazardous waste disposed [tonnes]"
    },
    {
      id: "CI13",
      title: "Hazardous Waste Recycling",
      type: 1, unit: 'tonnes',
      dcfIds: [297], allowedType: [2, 4, 5, 6],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: " sum of hazardous waste disposed through recycling, recovery, composting, waste-to-energy plants,... [tonnes]"
    },
    {
      id: "CI6",
      title: "Water Withdrawn",
      type: 3,
      dcfIds: [285],
      overallTags: [],
      methodology: "Sum of water withdrawn [m3]"
    },

    {
      id: "CI8",
      title: "Water Discharged",
      type: 3,
      dcfIds: [286],
      overallTags: [],
      methodology: "Sum of water discharged [m3]"
    },
    {
      id: "CI26",
      title: "GHG Scope 2 Market-based (not including EACs)",
      type: 1, unit: '%',
      dcfIds: [287],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "Grid electricity consumption [MWh] x Grid emission factor [tCO2e/energy unit]"
    },
    {
      id: "CI27",
      title: "Energy Use",
      type: 1, unit: 'MWh',
      dcfIds: [287],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "Grid electricity consumption [MWh] + renewable energy from PPA [MWh]+ renewable energy from green tariff [MWh] + renewable energy from self generation [MWh]"
    },
    {
      id: "CI28",
      title: "Renewable Energy Factor (Not including EACs)",
      type: 1, unit: '%',
      dcfIds: [287],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "Total Renewable electricity consumption from PPA, green tariff and self generation [MWh]  / Energy use [MWh] "
    },
    {
      "id": "CI29",
      "title": "Renewable Energy Factor (including EACs)",
      "dcfIds": [
        268,
        287
      ], overallTags: [], unit: '%',
      "type": 1, standalone_ids: [304, 305, 287],
      "methodology": "(Total Renewable electricity consumption from PPA, green tariff and self generation [MWh] + Total RECs retired [RECS])  / Energy use [MWh] ",

    },
    {
      id: "CI30",
      title: "Total Customer IT load",
      type: 1, unit: 'MWh',
      dcfIds: [264],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "sum of all customer IT load [MWh]"
    },
    {
      id: "CI31",
      title: "PUE",
      type: 1, unit: 'No Unit',
      dcfIds: [264, 287],
      overallTags: [], standalone_ids: [304, 305, 287],
      methodology: "Energy Use [MWh] /Total electricity consumption by customer IT [MWh]"
    }
  ]
  // Add frequency weights as a class property
  private frequencyWeights: FrequencyWeight = {
    monthly: 1,
    'bi-monthly': 2,
    quarterly: 3,
    'half-yearly': 4,
    yearly: 5,
    custom: 1
  };

  /**
   * Create standardized error response with proper error codes
   */
  private createErrorResponse(errorType: keyof typeof STT_REPORT_ERRORS, message: string, field?: string): {isValid: boolean; status: boolean; message: string; error_code: number; error_name: string; field?: string} {
    const error = STT_REPORT_ERRORS[errorType];
    return {
      isValid: false,
      status: false,
      message: message,
      error_code: error.appErrorCode,
      error_name: error.errorName,
      ...(field && {field})
    };
  }

  /**
   * Validate JWT token and extract user information
   */
  private async validateAuthentication(authHeader?: string): Promise<{isValid: boolean; userProfileId?: number; error?: any}> {
    try {
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'Authorization header missing or invalid format. Expected: Bearer <jwt_token>',
            error_code: 2000,
            error_name: 'UnauthorizedError',
            http_code: 401
          }
        };
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      if (!token) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'JWT token is missing',
            error_code: 2000,
            error_name: 'UnauthorizedError',
            http_code: 401
          }
        };
      }

      let payload: any;
      try {
        // Decode JWT token to get user information
        // Note: In a real implementation, you would verify the token signature
        payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      } catch (decodeError) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'Token malformed or invalid',
            error_code: 2003,
            error_name: 'InvalidTokenError',
            http_code: 401
          }
        };
      }

      if (!payload.email) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'Invalid JWT token: missing email identifier',
            error_code: 2003,
            error_name: 'InvalidTokenError',
            http_code: 401
          }
        };
      }

      // Check token expiration
      if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'Session token has expired',
            error_code: 2002,
            error_name: 'TokenExpiredError',
            http_code: 401
          }
        };
      }

      // Find user by email first (following your pattern)
      const userDetail = await this.userRepository.findOne({
        where: {email: payload.email}
      });

      if (!userDetail) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'User not found or invalid authentication',
            error_code: 2000,
            error_name: 'UnauthorizedError',
            http_code: 401
          }
        };
      }

      // Find user profile by user ID (following your pattern)
      const userProfile = await this.userProfileRepository.findOne({
        where: {userId: userDetail.id}
      });

      if (!userProfile) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'User not found or invalid authentication',
            error_code: 2000,
            error_name: 'UnauthorizedError',
            http_code: 401
          }
        };
      }

      return {
        isValid: true,
        userProfileId: userProfile.id
      };

    } catch (error) {
      console.error('Authentication validation error:', error);
      return {
        isValid: false,
        error: {
          status: false,
          message: 'Authentication failed due to server error',
          error_code: 2000,
          error_name: 'UnauthorizedError',
          http_code: 401
        }
      };
    }
  }

  /**
   * Validate user authorization (clientId = 94 and role = 24 or 6)
   */
  private async validateAuthorization(userProfileId: number): Promise<{
    isValid: boolean;
    error?: any;
    entityAccessInfo?: {
      hasFullAccess: boolean;
      allowedCountries?: number[];
      roleType: 'corporate_admin' | 'country_admin';
    }
  }> {
    try {
      // Check if user belongs to clientId 94
      const userProfile = await this.userProfileRepository.findById(userProfileId);

      if (!userProfile || userProfile.clientId !== 94) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'Access denied: User does not belong to the required client',
            error_code: 2001,
            error_name: 'ForbiddenError',
            http_code: 403
          }
        };
      }

      // Check if user has required roles (24 or 6)
      const userRoles = await this.userRoleAuthorizationRepository.find({
        where: {user_id: userProfileId, userProfileId: 94}
      });

      const hasRequiredRole = userRoles.some(userRole => {
        if (userRole.roles && Array.isArray(userRole.roles)) {
          return userRole.roles.includes(24) || userRole.roles.includes(6);
        }
        return false;
      });

      if (!hasRequiredRole) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'Access denied: User does not have required permissions (Country Admin or Corporate Admin)',
            error_code: 2001,
            error_name: 'ForbiddenError',
            http_code: 403
          }
        };
      }

      // After role validation, determine entity access based on role hierarchy
      let entityAccessInfo: {
        hasFullAccess: boolean;
        allowedCountries?: number[];
        roleType: 'corporate_admin' | 'country_admin';
      };

      // Check if user has Corporate Admin role (24) - this overrides Country Admin (6)
      const hasCorporateAdminRole = userRoles.some(userRole => {
        if (userRole.roles && Array.isArray(userRole.roles)) {
          return userRole.roles.includes(24);
        }
        return false;
      });

      if (hasCorporateAdminRole) {
        // Corporate Admin (role 24) has access to all entities
        entityAccessInfo = {
          hasFullAccess: true,
          roleType: 'corporate_admin'
        };
      } else {
        // User has only Country Admin role (6) - get their assigned countries
        const countryAdminRoles = await this.userRoleAuthorizationRepository.find({
          where: {
            user_id: userProfileId, userProfileId: 94
          }
        });

        // Extract tier1_id (country IDs) from country admin assignments where role 6 is present
        const allowedCountries: number[] = [];
        countryAdminRoles.forEach(roleAssignment => {
          // Check if this role assignment contains role 6 (Country Admin)
          if (roleAssignment.roles && Array.isArray(roleAssignment.roles) &&
            roleAssignment.roles.includes(6) && roleAssignment.tier1_id) {
            // tier1_id can be a single number or array of numbers
            if (Array.isArray(roleAssignment.tier1_id)) {
              allowedCountries.push(...roleAssignment.tier1_id);
            } else {
              allowedCountries.push(roleAssignment.tier1_id);
            }
          }
        });

        entityAccessInfo = {
          hasFullAccess: false,
          allowedCountries: [...new Set(allowedCountries)], // Remove duplicates
          roleType: 'country_admin'
        };
      }

      return {
        isValid: true,
        entityAccessInfo
      };

    } catch (error) {
      console.error('Authorization validation error:', error);
      return {
        isValid: false,
        error: {
          status: false,
          message: 'Authorization validation failed due to server error',
          error_code: 2001,
          error_name: 'ForbiddenError',
          http_code: 403
        }
      };
    }
  }

  /**
   * Validate entity access based on user role and permissions with proper hierarchy checking
   */
  private async validateEntityAccess(
    requestedEntities: string[],
    entityAccessInfo: {
      hasFullAccess: boolean;
      allowedCountries?: number[];
      roleType: 'corporate_admin' | 'country_admin';
    }
  ): Promise<{isValid: boolean; error?: any; filteredEntities?: string[]}> {
    try {
      // Corporate Admin (role 24) has access to all entities
      if (entityAccessInfo.hasFullAccess) {
        return {
          isValid: true,
          filteredEntities: requestedEntities
        };
      }

      // Country Admin (role 6) - validate entity access
      if (!entityAccessInfo.allowedCountries || entityAccessInfo.allowedCountries.length === 0) {
        return {
          isValid: false,
          error: {
            status: false,
            message: 'Access denied: Country Admin role requires assigned countries',
            error_code: 2001,
            error_name: 'ForbiddenError',
            http_code: 403
          }
        };
      }

      // Get entity names and hierarchy information
      const entityHierarchy = await this.getEntityHierarchyInfo(requestedEntities);

      const allowedEntities: string[] = [];
      const deniedEntities: {entity: string; name: string; parentCountry?: string}[] = [];

      for (const entity of requestedEntities) {
        const entityMatch = entity.match(/^(\d+)-(\d+)$/);
        if (entityMatch) {
          const level = parseInt(entityMatch[1]);
          const locationId = parseInt(entityMatch[2]);
          const entityInfo = entityHierarchy.get(entity);

          // Level 1 = Country, Level 2 = Region/State, Level 3 = City
          if (level === 1) {
            // Country level - check if user has access to this country
            if (entityAccessInfo.allowedCountries!.includes(locationId)) {
              allowedEntities.push(entity);
            } else {
              deniedEntities.push({
                entity,
                name: entityInfo?.name || entity,
                parentCountry: undefined
              });
            }
          } else if (level === 2) {
            // Region level - check if parent country is allowed
            const parentCountryId = entityInfo?.parentCountryId;
            if (parentCountryId && entityAccessInfo.allowedCountries!.includes(parentCountryId)) {
              allowedEntities.push(entity);
            } else {
              deniedEntities.push({
                entity,
                name: entityInfo?.name || entity,
                parentCountry: entityInfo?.parentCountryName || `Country ID: ${parentCountryId}`
              });
            }
          } else if (level === 3) {
            // City level - check if parent country is allowed
            const parentCountryId = entityInfo?.parentCountryId;
            if (parentCountryId && entityAccessInfo.allowedCountries!.includes(parentCountryId)) {
              allowedEntities.push(entity);
            } else {
              deniedEntities.push({
                entity,
                name: entityInfo?.name || entity,
                parentCountry: entityInfo?.parentCountryName || `Country ID: ${parentCountryId}`
              });
            }
          } else {
            deniedEntities.push({
              entity,
              name: entityInfo?.name || entity,
              parentCountry: undefined
            });
          }
        } else {
          deniedEntities.push({
            entity,
            name: entity,
            parentCountry: undefined
          });
        }
      }

      if (deniedEntities.length > 0) {
        // Get allowed country names for better error message
        const allowedCountryNames = await this.getCountryNames(entityAccessInfo.allowedCountries!);

        // Create detailed error message with entity names
        const deniedEntityDetails = deniedEntities.map(denied => {
          if (denied.parentCountry) {
            return `${denied.name} (${denied.entity}) in ${denied.parentCountry}`;
          } else {
            return `${denied.name} (${denied.entity})`;
          }
        });

        return {
          isValid: false,
          error: {
            status: false,
            message: `Access denied: Country Admin does not have access to entities: ${deniedEntityDetails.join(', ')}. Allowed countries: ${allowedCountryNames.join(', ')}`,
            error_code: 2001,
            error_name: 'ForbiddenError',
            http_code: 403
          }
        };
      }

      return {
        isValid: true,
        filteredEntities: allowedEntities
      };

    } catch (error) {
      console.error('Entity access validation error:', error);
      return {
        isValid: false,
        error: {
          status: false,
          message: 'Entity access validation failed due to server error',
          error_code: 2001,
          error_name: 'ForbiddenError',
          http_code: 403
        }
      };
    }
  }

  /**
   * Get entity hierarchy information including names and parent country IDs
   */
  private async getEntityHierarchyInfo(entities: string[]): Promise<Map<string, {name: string; parentCountryId?: number; parentCountryName?: string}>> {
    const entityMap = new Map<string, {name: string; parentCountryId?: number; parentCountryName?: string}>();

    try {
      // Get all location data with hierarchy using the same pattern as UserProfileController
      const locationOnes = await this.userProfileRepository.locationOnes(94).find({
        include: [
          {
            relation: "locationTwos",
            scope: {
              include: [{relation: "locationThrees"}],
            },
          },
        ],
      });

      // Build entity name mapping from the location hierarchy
      locationOnes.forEach((location: any) => {
        const level1Key = `1-${location.id}`;
        entityMap.set(level1Key, {
          name: location.name || `Country ${location.id}`,
          parentCountryId: location.id,
          parentCountryName: location.name || `Country ${location.id}`
        });

        location.locationTwos?.forEach((locationTwo: any) => {
          const level2Key = `2-${locationTwo.id}`;
          entityMap.set(level2Key, {
            name: locationTwo.name || `Region ${locationTwo.id}`,
            parentCountryId: location.id,
            parentCountryName: location.name || `Country ${location.id}`
          });

          locationTwo.locationThrees?.forEach((locationThree: any) => {
            const level3Key = `3-${locationThree.id}`;
            entityMap.set(level3Key, {
              name: locationThree.name || `City ${locationThree.id}`,
              parentCountryId: location.id,
              parentCountryName: location.name || `Country ${location.id}`
            });
          });
        });
      });

      return entityMap;
    } catch (error) {
      console.error('Error fetching entity hierarchy:', error);
      return entityMap;
    }
  }

  /**
   * Get entity name mapping for all entities
   */
  private async getEntityNameMapping(): Promise<Map<string, string>> {
    const entityNameMap = new Map<string, string>();

    try {
      // Get all location data with hierarchy
      const locationOnes = await this.userProfileRepository.locationOnes(94).find({
        include: [
          {
            relation: "locationTwos",
            scope: {
              include: [{relation: "locationThrees"}],
            },
          },
        ],
      });

      // Build entity name mapping from the location hierarchy
      locationOnes.forEach((location: any) => {
        const level1Key = `1-${location.id}`;
        entityNameMap.set(level1Key, location.name || `Country ${location.id}`);

        location.locationTwos?.forEach((locationTwo: any) => {
          const level2Key = `2-${locationTwo.id}`;
          entityNameMap.set(level2Key, locationTwo.name || `Region ${locationTwo.id}`);

          locationTwo.locationThrees?.forEach((locationThree: any) => {
            const level3Key = `3-${locationThree.id}`;
            entityNameMap.set(level3Key, locationThree.name || `City ${locationThree.id}`);
          });
        });
      });

    } catch (error) {
      console.error('Error fetching entity name mapping:', error);
    }

    return entityNameMap;
  }

  /**
   * Get country names from country IDs
   */
  private async getCountryNames(countryIds: number[]): Promise<string[]> {
    try {
      const locationOnes = await this.userProfileRepository.locationOnes(94).find();
      return countryIds.map(countryId => {
        const country = locationOnes.find((loc: any) => loc.id === countryId);
        return country?.name || `Country ${countryId}`;
      });
    } catch (error) {
      console.error('Error fetching country names:', error);
      return countryIds.map(id => `Country ${id}`);
    }
  }

  /**
   * Get assignment frequencies for DCF-Entity combinations
   */
  private async getAssignmentFrequencies(dcfIds: number[], entities: string[]): Promise<any[]> {
    try {
      const assignmentMap: any = []
      const frequencyMapping: {[key: number]: string} = {
        0: 'unknown',
        1: 'monthly',
        2: 'bi-monthly',
        3: 'quarterly',
        4: 'yearly',
        5: 'half-yearly'
      };

      // Extract entity IDs and levels from entity strings
      const entityConditions = entities.map(entity => {
        const entityMatch = entity.match(/^(\d+)-(\d+)$/);
        if (entityMatch) {
          return {
            level: parseInt(entityMatch[1]),
            locationId: parseInt(entityMatch[2])
          };
        }
        return null;
      }).filter(Boolean);

      if (entityConditions.length === 0) {
        return assignmentMap;
      }

      // Fetch assignments for the given DCF IDs and entities using AssignDcfEntityUser
      const assignments = await this.userProfileRepository.assignDcfEntityUsers(94).find({
        where: {
          and: [
            {dcfId: {inq: dcfIds}},
            {
              or: entityConditions.map(entity => ({
                and: [
                  {level: entity!.level},
                  {locationId: entity!.locationId}
                ]
              }))
            }
          ]
        }
      });

      // Build the assignment map
      assignments.forEach(assignment => {
        const expectedPeriods = this.userProfileController.calculateReportingPeriods(
          assignment.start_date,
          assignment.end_date,
          assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency, '');

        for (const rp of expectedPeriods) {
          const key = `${assignment.dcfId}-${assignment.level}-${assignment.locationId}-${getRPTextFormat(rp)}`;
          assignmentMap.push({
            ...assignment, key,
            frequency_name: frequencyMapping[assignment.frequency || 0] || 'unknown',
            frequency_code: assignment.frequency || 0
          });
        }

      });

      return assignmentMap;

    } catch (error) {
      console.error('Error fetching assignment frequencies:', error);
      return []
    }
  }


  /**
   * Get underlying assignment periods that contribute to a requested period
   * Returns month ranges instead of period codes (e.g., "Jan-2024 to Mar-2024" instead of "Q1-2024")
   */
  private getUnderlyingAssignmentPeriods(
    requestedPeriod: string,
    requestedFrequency: string,
    assignmentFrequency: string
  ): string[] {
    try {
      // If assignment frequency is same or less granular than requested, no underlying periods
      if (assignmentFrequency === requestedFrequency.toLowerCase() ||
        this.isLessGranular(assignmentFrequency, requestedFrequency.toLowerCase())) {
        return [];
      }

      // Parse the requested period to determine underlying periods
      const underlyingPeriods: string[] = [];

      // Handle different assignment frequencies
      if (assignmentFrequency === 'monthly') {
        // For monthly assignments, always show individual months
        if (requestedFrequency.toLowerCase() === 'quarterly') {
          // Q1-2024 -> Jan-2024, Feb-2024, Mar-2024
          const quarterMatch = requestedPeriod.match(/^Q(\d+)-(\d+)$/);
          if (quarterMatch) {
            const quarter = parseInt(quarterMatch[1]);
            const year = parseInt(quarterMatch[2]);
            const startMonth = (quarter - 1) * 3 + 1;

            for (let i = 0; i < 3; i++) {
              const month = startMonth + i;
              const monthName = this.getMonthName(month);
              underlyingPeriods.push(`${monthName}-${year}`);
            }
          }
        } else if (requestedFrequency.toLowerCase() === 'half-yearly') {
          // H1-2024 -> Jan-2024, Feb-2024, Mar-2024, Apr-2024, May-2024, Jun-2024
          const halfYearMatch = requestedPeriod.match(/^H(\d+)-(\d+)$/);
          if (halfYearMatch) {
            const half = parseInt(halfYearMatch[1]);
            const year = parseInt(halfYearMatch[2]);
            const startMonth = (half - 1) * 6 + 1;

            for (let i = 0; i < 6; i++) {
              const month = startMonth + i;
              const monthName = this.getMonthName(month);
              underlyingPeriods.push(`${monthName}-${year}`);
            }
          }
        } else if (requestedFrequency.toLowerCase() === 'yearly') {
          // 2024 -> Jan-2024, Feb-2024, ..., Dec-2024
          const yearMatch = requestedPeriod.match(/^(\d+)$/);
          if (yearMatch) {
            const year = parseInt(yearMatch[1]);
            for (let month = 1; month <= 12; month++) {
              const monthName = this.getMonthName(month);
              underlyingPeriods.push(`${monthName}-${year}`);
            }
          }
        } else if (requestedFrequency.toLowerCase() === 'monthly') {
          // For monthly requests with monthly assignments, check if it's a numeric format
          const monthMatch = requestedPeriod.match(/^(\d{2})-(\d{4})$/);
          if (monthMatch) {
            const monthNum = parseInt(monthMatch[1]);
            const year = parseInt(monthMatch[2]);
            const monthName = this.getMonthName(monthNum);
            underlyingPeriods.push(`${monthName}-${year}`);
          }
        }
      } else if (assignmentFrequency === 'quarterly') {
        // For quarterly assignments, show month ranges
        if (requestedFrequency.toLowerCase() === 'half-yearly') {
          // H1-2024 -> "Jan-2024 to Mar-2024", "Apr-2024 to Jun-2024"
          const halfYearMatch = requestedPeriod.match(/^H(\d+)-(\d+)$/);
          if (halfYearMatch) {
            const half = parseInt(halfYearMatch[1]);
            const year = parseInt(halfYearMatch[2]);
            const startQuarter = (half - 1) * 2 + 1;

            for (let i = 0; i < 2; i++) {
              const quarter = startQuarter + i;
              const startMonth = (quarter - 1) * 3 + 1;
              const endMonth = startMonth + 2;
              const startMonthName = this.getMonthName(startMonth);
              const endMonthName = this.getMonthName(endMonth);
              underlyingPeriods.push(`${startMonthName}-${year} to ${endMonthName}-${year}`);
            }
          }
        } else if (requestedFrequency.toLowerCase() === 'yearly') {
          // 2024 -> "Jan-2024 to Mar-2024", "Apr-2024 to Jun-2024", "Jul-2024 to Sep-2024", "Oct-2024 to Dec-2024"
          const yearMatch = requestedPeriod.match(/^(\d+)$/);
          if (yearMatch) {
            const year = parseInt(yearMatch[1]);
            for (let quarter = 1; quarter <= 4; quarter++) {
              const startMonth = (quarter - 1) * 3 + 1;
              const endMonth = startMonth + 2;
              const startMonthName = this.getMonthName(startMonth);
              const endMonthName = this.getMonthName(endMonth);
              underlyingPeriods.push(`${startMonthName}-${year} to ${endMonthName}-${year}`);
            }
          }
        }
      } else if (assignmentFrequency === 'bi-monthly') {
        // For bi-monthly assignments, show month ranges
        if (requestedFrequency.toLowerCase() === 'quarterly') {
          // Q1-2024 -> "Jan-2024 to Feb-2024", "Mar-2024"
          const quarterMatch = requestedPeriod.match(/^Q(\d+)-(\d+)$/);
          if (quarterMatch) {
            const quarter = parseInt(quarterMatch[1]);
            const year = parseInt(quarterMatch[2]);
            const startMonth = (quarter - 1) * 3 + 1;

            // First bi-month of the quarter
            const firstBiMonthStart = startMonth;
            const firstBiMonthEnd = startMonth + 1;
            underlyingPeriods.push(`${this.getMonthName(firstBiMonthStart)}-${year} to ${this.getMonthName(firstBiMonthEnd)}-${year}`);

            // Third month (single month)
            const thirdMonth = startMonth + 2;
            underlyingPeriods.push(`${this.getMonthName(thirdMonth)}-${year}`);
          }
        } else if (requestedFrequency.toLowerCase() === 'half-yearly') {
          // H1-2024 -> "Jan-2024 to Feb-2024", "Mar-2024 to Apr-2024", "May-2024 to Jun-2024"
          const halfYearMatch = requestedPeriod.match(/^H(\d+)-(\d+)$/);
          if (halfYearMatch) {
            const half = parseInt(halfYearMatch[1]);
            const year = parseInt(halfYearMatch[2]);
            const startMonth = (half - 1) * 6 + 1;

            for (let i = 0; i < 3; i++) {
              const biMonthStart = startMonth + (i * 2);
              const biMonthEnd = biMonthStart + 1;
              underlyingPeriods.push(`${this.getMonthName(biMonthStart)}-${year} to ${this.getMonthName(biMonthEnd)}-${year}`);
            }
          }
        } else if (requestedFrequency.toLowerCase() === 'yearly') {
          // 2024 -> "Jan-2024 to Feb-2024", "Mar-2024 to Apr-2024", ..., "Nov-2024 to Dec-2024"
          const yearMatch = requestedPeriod.match(/^(\d+)$/);
          if (yearMatch) {
            const year = parseInt(yearMatch[1]);
            for (let biMonth = 1; biMonth <= 6; biMonth++) {
              const startMonth = (biMonth - 1) * 2 + 1;
              const endMonth = startMonth + 1;
              underlyingPeriods.push(`${this.getMonthName(startMonth)}-${year} to ${this.getMonthName(endMonth)}-${year}`);
            }
          }
        }
      } else if (assignmentFrequency === 'half-yearly') {
        // For half-yearly assignments, show month ranges
        if (requestedFrequency.toLowerCase() === 'yearly') {
          // 2024 -> "Jan-2024 to Jun-2024", "Jul-2024 to Dec-2024"
          const yearMatch = requestedPeriod.match(/^(\d+)$/);
          if (yearMatch) {
            const year = parseInt(yearMatch[1]);
            underlyingPeriods.push(`Jan-${year} to Jun-${year}`);
            underlyingPeriods.push(`Jul-${year} to Dec-${year}`);
          }
        }
      } else if (assignmentFrequency === 'half-yearly') {
        // For half-yearly assignments, show month ranges
        if (requestedFrequency.toLowerCase() === 'yearly') {
          // 2024 -> "Jan-2024 to Jun-2024", "Jul-2024 to Dec-2024"
          const yearMatch = requestedPeriod.match(/^(\d+)$/);
          if (yearMatch) {
            const year = parseInt(yearMatch[1]);
            underlyingPeriods.push(`Jan-${year} to Jun-${year}`);
            underlyingPeriods.push(`Jul-${year} to Dec-${year}`);
          }
        }
      }

      return underlyingPeriods;

    } catch (error) {
      console.error('Error getting underlying assignment periods:', error);
      return [];
    }
  }

  /**
   * Check if frequency1 is less granular than frequency2
   */
  private isLessGranular(frequency1: string, frequency2: string): boolean {
    const granularityOrder = ['monthly', 'bi-monthly', 'quarterly', 'half-yearly', 'yearly'];
    const index1 = granularityOrder.indexOf(frequency1);
    const index2 = granularityOrder.indexOf(frequency2);
    return index1 > index2;
  }

  private getFrequencyFromCode(code: number): string {
    switch (code) {
      case 1: return 'monthly';
      case 2: return 'bi-monthly';
      case 3: return 'quarterly';
      case 4: return 'yearly';
      case 5: return 'half-yearly';
      case 6: return 'custom';
      default: throw new Error(`Invalid frequency code: ${code}`);
    }
  }

  private validateFrequencyCompatibility(
    requestedFrequency: string,
    assignment: any,
    customPeriodFrom?: string,
    customPeriodTo?: string,
    year?: string | number | number[]
  ): {isValid: boolean; incompatibleReason?: string} {
    // First validate that the requested period falls within the assignment period
    const assignmentStart = new Date(assignment.start_date);
    const assignmentEnd = assignment.end_date ? new Date(assignment.end_date) : new Date();

    // Normalize the requested frequency
    const normalizedFrequency = requestedFrequency.toLowerCase();

    // Different date range validation for custom vs non-custom periods
    if (normalizedFrequency === 'custom') {
      // For custom periods, validate using the exact from/to dates
      if (!customPeriodFrom || !customPeriodTo) {
        return {
          isValid: false,
          incompatibleReason: 'Custom period requires both start and end dates'
        };
      }

      const requestStart = new Date(customPeriodFrom);
      const requestEnd = new Date(customPeriodTo);

      // Set times to start and end of day for accurate comparison
      requestStart.setUTCHours(0, 0, 0, 0);
      requestEnd.setUTCHours(23, 59, 59, 999);
      assignmentStart.setUTCHours(0, 0, 0, 0);
      assignmentEnd.setUTCHours(23, 59, 59, 999);

      if (requestStart < assignmentStart || requestEnd > assignmentEnd) {
        return {
          isValid: false,
          incompatibleReason: `Requested period (${customPeriodFrom} to ${customPeriodTo}) must fall within the assignment period (${assignment.start_date.split('T')[0]} to ${assignment.end_date ? assignment.end_date.split('T')[0] : 'present'})`
        };
      }
    } else {
      // For non-custom frequencies, validate using the year
      if (!year) {
        return {
          isValid: false,
          incompatibleReason: 'Year is required for non-custom periods'
        };
      }

      // Handle year as either string, number, or array
      let requestedYear: number;
      if (Array.isArray(year)) {
        // For array, use the first year for validation
        requestedYear = year[0];
      } else if (typeof year === 'number') {
        requestedYear = year;
      } else {
        requestedYear = parseInt(year);
      }

      const assignmentStartYear = assignmentStart.getUTCFullYear();
      const assignmentEndYear = assignmentEnd.getUTCFullYear();

      if (requestedYear < assignmentStartYear || requestedYear > assignmentEndYear) {
        return {
          isValid: false,
          incompatibleReason: `Requested year ${requestedYear} must fall within the assignment period (${assignmentStartYear} to ${assignmentEndYear})`
        };
      }
    }

    // Get assignment frequency details
    const assignmentFreq = this.getFrequencyFromCode(assignment.frequency);

    // Define compatibility matrix - assignment frequency -> supported requested frequencies
    const compatibilityMatrix: {[key: string]: string[]} = {
      'monthly': ['monthly', 'bi-monthly', 'quarterly', 'half-yearly', 'yearly'],  // Monthly can support all
      'bi-monthly': ['bi-monthly', 'half-yearly', 'yearly'],  // Bi-monthly can support bi-monthly, half-yearly, yearly (NOT quarterly)
      'quarterly': ['quarterly', 'half-yearly', 'yearly'],  // Quarterly can support quarterly, half-yearly, yearly
      'half-yearly': ['half-yearly', 'yearly'],  // Half-yearly can support half-yearly, yearly
      'yearly': ['yearly'],  // Yearly can only support yearly
      'custom': ['monthly', 'bi-monthly', 'quarterly', 'half-yearly', 'yearly', 'custom']
    };

    const canSupport = compatibilityMatrix[assignmentFreq]?.includes(normalizedFrequency) || false;

    if (!canSupport) {
      const supportedFrequencies = compatibilityMatrix[assignmentFreq] || [];
      let errorMessage = `Assignment frequency (${assignmentFreq}) cannot support requested frequency (${normalizedFrequency}). `;

      if (normalizedFrequency === 'quarterly' && assignmentFreq === 'bi-monthly') {
        errorMessage += `Bi-monthly assignments cannot support quarterly reporting because bi-monthly periods (Jan-Feb, Mar-Apr, etc.) do not align with quarterly periods (Q1, Q2, Q3, Q4).`;
      } else {
        errorMessage += `Assignment with ${assignmentFreq} frequency can only support: ${supportedFrequencies.join(', ')}. Requested: ${normalizedFrequency}.`;
      }

      return {
        isValid: false,
        incompatibleReason: errorMessage
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate basic input requirements before processing
   */
  private validateBasicInputRequirements(requestData: any): {isValid: boolean; status?: boolean; message?: string; error_code?: number; error_name?: string; field?: string} {
    const {
      main_data_source,
      sub_data_source,
      raw_parameters,
      indicator_parameters,
      filter_type,
      applied_common_filter,
      applied_specific_filter,
      type_of_data,
      type_of_format,
      table_config,
      chart_config
    } = requestData;

    // 1. Check required fields
    if (!main_data_source) {
      return this.createErrorResponse('MISSING_FIELD_ERROR', 'main_data_source is required', 'main_data_source');
    }

    if (!type_of_data) {
      return this.createErrorResponse('MISSING_FIELD_ERROR', 'type_of_data is required and must be either "queried_data" or "direct_extract"', 'type_of_data');
    }

    if (!type_of_format) {
      return this.createErrorResponse('MISSING_FIELD_ERROR', 'type_of_format is required and must be one of "tabular_form_data", "value_field", or "chart_data"', 'type_of_format');
    }

    if (!filter_type) {
      return this.createErrorResponse('MISSING_FIELD_ERROR', 'filter_type is required and must be either "applied_common_filter" or "applied_specific_filter"', 'filter_type');
    }

    // 2. Conditional validation for main_data_source = quantitative
    if (main_data_source === 'quantitative') {
      if (!sub_data_source) {
        return this.createErrorResponse('DEPENDENCY_VALIDATION_ERROR', 'sub_data_source is required when main_data_source is "quantitative"', 'sub_data_source');
      }

      // Check raw_parameters when sub_data_source is raw
      if (sub_data_source === 'raw') {
        if (!raw_parameters) {
          return this.createErrorResponse('DEPENDENCY_VALIDATION_ERROR', 'raw_parameters is required when main_data_source is "quantitative" and sub_data_source is "raw"', 'raw_parameters');
        }
      }

      // Check indicator_parameters when sub_data_source is indicator
      if (sub_data_source === 'indicator') {
        if (!indicator_parameters) {
          return this.createErrorResponse('DEPENDENCY_VALIDATION_ERROR', 'indicator_parameters is required when main_data_source is "quantitative" and sub_data_source is "indicator"', 'indicator_parameters');
        }
      }
    }

    // 3. Validate filter presence
    const activeFilterType = filter_type === 'applied_common_filter' ? applied_common_filter : filter_type === 'applied_specific_filter' ? applied_specific_filter : null;

    if (!activeFilterType) {
      return {
        isValid: false,
        message: `${filter_type} object is required but not provided`
      };
    }

    // 4. Validate filter content
    const filterValidation = this.validateFilterContent(activeFilterType, filter_type);
    if (!filterValidation.isValid) {
      return filterValidation;
    }

    // 5. Validate type_of_data specific requirements
    if (type_of_data === 'queried_data') {
      if (!requestData.query_details) {
        return {
          isValid: false,
          message: 'query_details is required when type_of_data is "queried_data"'
        };
      }

      const queryValidation = this.validateQueryDetails(requestData.query_details);
      if (!queryValidation.isValid) {
        return queryValidation;
      }
    }

    // 6. Validate type_of_format specific requirements
    if (type_of_format === 'tabular_form_data') {
      if (!table_config) {
        return {
          isValid: false,
          message: 'table_config is required when type_of_format is "tabular_form_data"'
        };
      }

      // Validate entity breakdown requirements for tabular format
      const tabularValidation = this.validateBreakdownRequirements(table_config, activeFilterType, 'table_config');
      if (!tabularValidation.isValid) {
        return tabularValidation;
      }

      // Note: If no breakdown is explicitly set, period_breakdown will be used as default
    }

    if (type_of_format === 'chart_data') {
      if (!chart_config) {
        return {
          isValid: false,
          message: 'chart_config is required when type_of_format is "chart_data"'
        };
      }

      // Validate entity breakdown requirements for chart format
      const chartValidation = this.validateBreakdownRequirements(chart_config, activeFilterType, 'chart_config');
      if (!chartValidation.isValid) {
        return chartValidation;
      }

      // Note: If no breakdown is explicitly set, period_breakdown will be used as default
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate query_details requirements for queried_data type
   */
  private validateQueryDetails(query_details: any): {isValid: boolean; message?: string} {
    if (!query_details.query_type) {
      return {
        isValid: false,
        message: 'query_type is required in query_details'
      };
    }

    const validQueryTypes = ['sum', 'ratio', 'percentage', 'count'];
    if (!validQueryTypes.includes(query_details.query_type)) {
      return {
        isValid: false,
        message: `query_type must be one of: ${validQueryTypes.join(', ')}`
      };
    }

    // Validate based on query_type
    if (query_details.query_type === 'sum') {
      // For sum, query_parameters is not required
      if (query_details.query_parameters) {
        return this.validateSumQueryParameters(query_details.query_parameters);
      }
    } else if (query_details.query_type === 'ratio') {
      // For ratio, query_parameters is required
      if (!query_details.query_parameters) {
        return {
          isValid: false,
          message: 'query_parameters is required when query_type is "ratio"'
        };
      }
      return this.validatePercentageRatioQueryParameters(query_details.query_parameters);
    } else if (query_details.query_type === 'percentage') {
      // For percentage, query_parameters is not required for now
      if (query_details.query_parameters) {
        return this.validatePercentageRatioQueryParameters(query_details.query_parameters);
      }
    }

    // For now, only sum is implemented
    if (query_details.query_type !== 'sum') {
      return {
        isValid: false,
        message: `query_type "${query_details.query_type}" is not yet implemented. Currently only "sum" is supported.`
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate query parameters for sum query type
   */
  private validateSumQueryParameters(query_parameters: any): {isValid: boolean; message?: string} {
    if (!query_parameters.entity_selected) {
      return {
        isValid: false,
        message: 'entity_selected is required in query_parameters for sum query_type'
      };
    }

    if (!Array.isArray(query_parameters.entity_selected)) {
      return {
        isValid: false,
        message: 'entity_selected must be an array'
      };
    }

    if (!query_parameters.reporting_period) {
      return {
        isValid: false,
        message: 'reporting_period is required in query_parameters for sum query_type'
      };
    }

    // Check for either dcf_name or indicator_name
    const hasDcfName = query_parameters.dcf_name && Array.isArray(query_parameters.dcf_name) && query_parameters.dcf_name.length > 0;
    const hasIndicatorName = query_parameters.indicator_name && Array.isArray(query_parameters.indicator_name) && query_parameters.indicator_name.length > 0;

    if (!hasDcfName && !hasIndicatorName) {
      return {
        isValid: false,
        message: 'Either dcf_name or indicator_name array is required in query_parameters for sum query_type'
      };
    }

    if (hasDcfName && hasIndicatorName) {
      return {
        isValid: false,
        message: 'Only one of dcf_name or indicator_name should be provided, not both'
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate query parameters for percentage/ratio query types
   */
  private validatePercentageRatioQueryParameters(query_parameters: any): {isValid: boolean; message?: string} {
    if (!query_parameters.entity_selected) {
      return {
        isValid: false,
        message: 'entity_selected is required in query_parameters for percentage/ratio query_type'
      };
    }

    if (!query_parameters.reporting_period) {
      return {
        isValid: false,
        message: 'reporting_period is required in query_parameters for percentage/ratio query_type'
      };
    }

    if (!query_parameters.parameter_a) {
      return {
        isValid: false,
        message: 'parameter_a is required in query_parameters for percentage/ratio query_type'
      };
    }

    if (!query_parameters.parameter_b) {
      return {
        isValid: false,
        message: 'parameter_b is required in query_parameters for percentage/ratio query_type'
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate breakdown requirements for tabular_form_data and chart_data formats
   */
  private validateBreakdownRequirements(config: any, activeFilterType: any, configName: string): {isValid: boolean; message?: string} {
    // Check if entity_breakdown is enabled
    if (config.entity_breakdown === true) {
      // When entity_breakdown is true, entity array must have exactly 1 element
      if (!activeFilterType.entity || !Array.isArray(activeFilterType.entity)) {
        return {
          isValid: false,
          message: `When ${configName}.entity_breakdown is true, applied_specific_filter.entity must be an array`
        };
      }

      if (activeFilterType.entity.length !== 1) {
        return {
          isValid: false,
          message: `When ${configName}.entity_breakdown is true, applied_specific_filter.entity must contain exactly 1 element. Current: ${activeFilterType.entity.length} elements.`
        };
      }
    } else {
      // For other breakdown cases (period, dcf, dp), validate that only one of entity or year can be multiple
      if (activeFilterType.entity && activeFilterType.year &&
        Array.isArray(activeFilterType.entity) && Array.isArray(activeFilterType.year)) {

        const entityLength = activeFilterType.entity.length;
        const yearLength = activeFilterType.year.length;

        // Both cannot be multiple (> 1)
        if (entityLength > 1 && yearLength > 1) {
          return {
            isValid: false,
            message: `For ${configName} with breakdown other than entity_breakdown, only one of entity or year can have multiple values, not both. Current: entity has ${entityLength} values, year has ${yearLength} values.`
          };
        }
      }
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate filter content requirements
   */
  private validateFilterContent(activeFilterType: any, filterTypeName: string): {isValid: boolean; message?: string} {
    if (!activeFilterType.year) {
      return {
        isValid: false,
        message: `year is required in ${filterTypeName}`
      };
    }

    if (!Array.isArray(activeFilterType.year)) {
      return {
        isValid: false,
        message: `year must be an array of numbers in ${filterTypeName}`
      };
    }

    if (activeFilterType.year.length === 0) {
      return {
        isValid: false,
        message: `year array cannot be empty in ${filterTypeName}`
      };
    }

    if (!activeFilterType.year.every((year: any) => typeof year === 'number' && !isNaN(year))) {
      return {
        isValid: false,
        message: `year array must contain only valid numbers in ${filterTypeName}`
      };
    }

    // Check for duplicate years
    const uniqueYears = new Set(activeFilterType.year);
    if (uniqueYears.size !== activeFilterType.year.length) {
      const duplicates = activeFilterType.year.filter((year: any, index: number) =>
        activeFilterType.year.indexOf(year) !== index
      );
      return {
        isValid: false,
        message: `Duplicate years found in ${filterTypeName}: ${[...new Set(duplicates)].join(', ')}. Each year must be unique.`
      };
    }

    if (!activeFilterType.reporting_period) {
      return {
        isValid: false,
        message: `reporting_period is required in ${filterTypeName}`
      };
    }

    // Validate Custom period requirements
    if (activeFilterType.reporting_period === 'Custom') {
      if (!activeFilterType.reporting_period_from) {
        return {
          isValid: false,
          message: `reporting_period_from is required when reporting_period is "Custom" in ${filterTypeName}`
        };
      }
      if (!activeFilterType.reporting_period_to) {
        return {
          isValid: false,
          message: `reporting_period_to is required when reporting_period is "Custom" in ${filterTypeName}`
        };
      }
    }

    if (!activeFilterType.entity) {
      return {
        isValid: false,
        message: `entity is required in ${filterTypeName}`
      };
    }

    if (!Array.isArray(activeFilterType.entity)) {
      return {
        isValid: false,
        message: `entity must be an array of strings in ${filterTypeName}`
      };
    }

    if (activeFilterType.entity.length === 0) {
      return {
        isValid: false,
        message: `entity array cannot be empty in ${filterTypeName}`
      };
    }

    if (!activeFilterType.entity.every((entity: any) => typeof entity === 'string')) {
      return {
        isValid: false,
        message: `entity array must contain only strings in ${filterTypeName}`
      };
    }

    // Check for duplicate entities
    const uniqueEntities = new Set(activeFilterType.entity);
    if (uniqueEntities.size !== activeFilterType.entity.length) {
      const duplicates = activeFilterType.entity.filter((entity: any, index: number) =>
        activeFilterType.entity.indexOf(entity) !== index
      );
      return {
        isValid: false,
        message: `Duplicate entities found in ${filterTypeName}: ${[...new Set(duplicates)].join(', ')}. Each entity must be unique.`
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate year and entity requirements based on type_of_data
   * Year & entity values are taken from the filter_type object (applied_common_filter or applied_specific_filter)
   */
  private async validateYearAndEntityForDataType(type_of_data: string, activeFilterType: any): Promise<{isValid: boolean; status?: boolean; message?: string; error_code?: number; error_name?: string; field?: string}> {
    if (!activeFilterType.year || !activeFilterType.entity) {
      return this.createErrorResponse('MISSING_FIELD_ERROR', 'Both year and entity are required', !activeFilterType.year ? 'year' : 'entity');
    }

    // Year should always be an array now based on our schema
    if (!Array.isArray(activeFilterType.year)) {
      return this.createErrorResponse('INVALID_FORMAT_ERROR', 'Year must be an array of numbers', 'year');
    }

    if (!Array.isArray(activeFilterType.entity)) {
      return this.createErrorResponse('INVALID_FORMAT_ERROR', 'Entity must be an array of strings', 'entity');
    }

    const yearLength = activeFilterType.year.length;
    const entityLength = activeFilterType.entity.length;

    // Get current format for better error messages
    const currentYearFormat = `array with ${yearLength} element(s)`;
    const currentEntityFormat = `array with ${entityLength} element(s)`;

    // Validate entities and expand Level 1/2 to Level 3 if needed
    const entityValidationResult = await this.validateAndExpandEntities(activeFilterType.entity);
    if (!entityValidationResult.isValid) {
      return entityValidationResult;
    }

    // Update the entity array with expanded level 3 entities
    activeFilterType.entity = entityValidationResult.expandedEntities;

    if (type_of_data === 'queried_data') {
      // For queried_data: year should be array of numbers, minimum length 1 for both
      // Only one can be multiple (not both)
      if (yearLength < 1) {
        return {
          isValid: false,
          message: 'Year array must have at least 1 element'
        };
      }

      if (entityLength < 1) {
        return {
          isValid: false,
          message: 'Entity array must have at least 1 element'
        };
      }

      // Check that only one can be multiple (not both)
      if (yearLength > 1 && entityLength > 1) {
        return {
          isValid: false,
          message: `For queried_data, only one of year or entity can have multiple values, not both. Current: year has ${yearLength} values, entity has ${entityLength} values.`
        };
      }

      // Validate that year array contains only numbers
      if (!activeFilterType.year.every((year: any) => typeof year === 'number' && !isNaN(year))) {
        return {
          isValid: false,
          message: 'Year array must contain only valid numbers'
        };
      }

    } else if (type_of_data === 'direct_extract') {
      // For direct_extract: year should have exactly length 1, entity can be multiple
      if (yearLength !== 1) {
        return {
          isValid: false,
          message: `For direct_extract, year array must have exactly 1 element. Current format: ${currentYearFormat}.`
        };
      }

      if (entityLength < 1) {
        return {
          isValid: false,
          message: `For direct_extract, entity array must have at least 1 element. Current format: ${currentEntityFormat}.`
        };
      }

      // Validate year is a number
      const yearValue = activeFilterType.year[0];
      if (typeof yearValue !== 'number' || isNaN(yearValue)) {
        return {
          isValid: false,
          message: 'Year must be a valid number'
        };
      }
    }

    return {
      isValid: true
    };
  }



  /**
   * Validate entities and expand Level 1/2 to Level 3 entities
   * - Validates that all entities are at the same level (multi-level not supported)
   * - Validates that all entity IDs exist in the system
   * - Expands Level 1 and Level 2 entities to their corresponding Level 3 entities
   * - Keeps Level 3 entities as-is (no extraction required)
   */
  private async validateAndExpandEntities(entities: string[]): Promise<{isValid: boolean; status?: boolean; message?: string; error_code?: number; error_name?: string; field?: string; expandedEntities?: string[]}> {
    try {
      // Get location data to build shapedSite
      const filteredLocations = await this.userProfileRepository.locationOnes(94).find({
        include: [
          {
            relation: "locationTwos",
            scope: {
              include: [{relation: "locationThrees"}],
            },
          },
        ],
      });

      const shapedSite = filteredLocations.map(item => {
        if (item.locationTwos) {
          item.locationTwos = item.locationTwos.filter(locationTwo =>
            locationTwo.locationThrees && locationTwo.locationThrees.length > 0
          );
        }
        return item;
      }).filter(item => item.locationTwos && item.locationTwos.length > 0);

      // Create entity validation maps and expansion maps
      const validLevel1Keys = new Set<string>();
      const validLevel2Keys = new Set<string>();
      const validLevel3Keys = new Set<string>();

      // Maps for expansion
      const level1ToLevel3Map = new Map<string, string[]>();
      const level2ToLevel3Map = new Map<string, string[]>();

      shapedSite.forEach(location => {
        const level1Key = `1-${location.id}`;
        validLevel1Keys.add(level1Key);
        const level3Entities: string[] = [];

        location.locationTwos?.forEach((locationTwo: any) => {
          const level2Key = `2-${locationTwo.id}`;
          validLevel2Keys.add(level2Key);
          const level2Level3Entities: string[] = [];

          locationTwo.locationThrees?.forEach((locationThree: any) => {
            const level3Key = `3-${locationThree.id}`;
            validLevel3Keys.add(level3Key);
            level3Entities.push(level3Key);
            level2Level3Entities.push(level3Key);
          });

          // Map level 2 to its level 3 entities
          level2ToLevel3Map.set(level2Key, level2Level3Entities);
        });

        // Map level 1 to all its level 3 entities
        level1ToLevel3Map.set(level1Key, level3Entities);
      });

      // Validate each entity and collect levels
      const formatInvalidEntities: string[] = [];
      const levelInvalidEntities: string[] = [];
      const locationInvalidEntities: string[] = [];
      const entityLevels: number[] = [];

      for (const entity of entities) {
        // Check if entity follows the level-id format
        const entityMatch = entity.match(/^(\d+)-(\d+)$/);
        if (!entityMatch) {
          formatInvalidEntities.push(entity);
          continue;
        }

        const level = parseInt(entityMatch[1]);

        // Validate level is 1, 2, or 3
        if (level < 1 || level > 3) {
          levelInvalidEntities.push(entity);
          continue;
        }

        // Check if entity exists in valid locations based on level
        let entityExists = false;
        if (level === 1) {
          entityExists = validLevel1Keys.has(entity);
        } else if (level === 2) {
          entityExists = validLevel2Keys.has(entity);
        } else if (level === 3) {
          entityExists = validLevel3Keys.has(entity);
        }

        if (!entityExists) {
          locationInvalidEntities.push(entity);
          continue;
        }

        entityLevels.push(level);
      }

      // Check for different types of invalid entities and provide specific error messages
      if (formatInvalidEntities.length > 0) {
        return this.createErrorResponse('INVALID_FORMAT_ERROR', `Invalid entity format found: ${formatInvalidEntities.join(', ')}. Entities must be in format "level-id" (e.g., "1-12", "2-56", "3-72").`, 'entity');
      }

      if (levelInvalidEntities.length > 0) {
        return this.createErrorResponse('VALUE_OUT_OF_RANGE_ERROR', `Invalid entity level found: ${levelInvalidEntities.join(', ')}. Level must be 1, 2, or 3 (e.g., "1-12", "2-56", "3-72").`, 'entity');
      }

      if (locationInvalidEntities.length > 0) {
        return this.createErrorResponse('VALIDATION_ERROR', `Invalid location IDs found: ${locationInvalidEntities.join(', ')}. These location IDs do not exist in the system. Please verify the location IDs are correct.`, 'entity');
      }

      // Check if all entities are at the same level
      if (entityLevels.length > 1) {
        const firstLevel = entityLevels[0];
        const allSameLevel = entityLevels.every(level => level === firstLevel);

        if (!allSameLevel) {
          const levelCounts = entityLevels.reduce((acc, level) => {
            acc[level] = (acc[level] || 0) + 1;
            return acc;
          }, {} as Record<number, number>);

          const levelSummary = Object.entries(levelCounts)
            .map(([level, count]) => `level ${level}: ${count} entities`)
            .join(', ');

          return this.createErrorResponse('DEPENDENCY_VALIDATION_ERROR', `All entities must be at the same level. Found entities at different levels: ${levelSummary}. Please ensure all entities are at the same level (e.g., all level 1, or all level 2, or all level 3).`, 'entity');
        }
      }

      // All validations passed, now expand entities to Level 3
      const expandedEntities: string[] = [];

      for (const entity of entities) {
        const entityMatch = entity.match(/^(\d+)-(\d+)$/);
        if (!entityMatch) continue; // This should not happen as we already validated

        const level = parseInt(entityMatch[1]);

        if (level === 1) {
          // Level 1: Expand to all level 3 entities under this level 1
          const level3Entities = level1ToLevel3Map.get(entity) || [];
          expandedEntities.push(...level3Entities);
        } else if (level === 2) {
          // Level 2: Expand to all level 3 entities under this level 2
          const level3Entities = level2ToLevel3Map.get(entity) || [];
          expandedEntities.push(...level3Entities);
        } else if (level === 3) {
          // Level 3: Keep as-is (no extraction required)
          expandedEntities.push(entity);
        }
      }

      // Remove duplicates from expanded entities (in case multiple level 1/2 entities expand to same level 3)
      const uniqueExpandedEntities = [...new Set(expandedEntities)];

      return {
        isValid: true,
        expandedEntities: uniqueExpandedEntities
      };

    } catch (error) {
      return {
        isValid: false,
        message: `Error validating entities: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Calculate missing data information based on assignments
   * Identifies which entities and reporting periods have no data
   * Shows consolidated period ranges and actual entity names
   * Now properly handles different assignment frequencies for different DCFs
   */
  private async calculateMissingData(
    dcfIds: number[],
    validPeriods: string[],
    entities: string[],
    periodData: any[],
    requestedReportingPeriod?: string
  ): Promise<any[]> {
    try {
   
      const missingData: any[] = [];
      // Create a set of existing data combinations for quick lookup
      const existingDataCombinations = new Set<string>();
      // Get entity name mapping
      const entityNameMap = await this.getEntityNameMapping();

      // Fetch assignment information to get actual assignment frequencies
      const assignmentInfo = await this.getAssignmentFrequencies(dcfIds, entities);
      console.log(assignmentInfo)
      // Build the set of existing data combinations
      periodData.forEach((periodObj: any, periodIndex: number) => {
        const period = validPeriods[periodIndex];
        const dataArray = (periodObj?.data || []).map((dataItem: any) => ({...dataItem, key: `${dataItem.dcfId}-${dataItem.level}-${dataItem.locationId}-${dataItem.reporting_period}`}))
        for (const assignment of assignmentInfo) {

          const found = dataArray.find((x: any) => x.key === assignment.key)
          if (found) {
            let index = missingData.findIndex((x: any) => x.key === assignment.key)
            if (index !== -1) {
              missingData.splice(index, 1)
            }
            // console.log('found')
          } else {
            const entityName = entityNameMap.get(assignment.level + '-' + assignment.locationId)
            missingData.push({
              dcf_id: assignment.dcfId,
              entity_id: assignment.entityId,
              entity: entityName, key: assignment.key,
              requested_period: period,
              reporting_period: assignment?.frequency_name || 'Unknown',
              reason: 'No data found for this DCF-Entity-Period combination'
            });
            // console.log('not found')
          }

        }
        // Extract the actual data array from the period object


        // if (dataArray && dataArray.length > 0) {
        //   dataArray.forEach((dataItem: any) => {
        //     // Create a unique key for each data combination
        //     const key = `${dataItem.dcfId}-${dataItem.level}-${dataItem.locationId}-${dataItem.reporting_period}`;
        //     // console.log('key1', key)
        //     const found = assignmentInfo.find(x => x.key === key)

        //     console.log(found ? found : 'not found')
        //     existingDataCombinations.add(key);
        //   });
        // } else {
        //   console.log(`No data for period ${period} - data array is empty or missing`);
        // }
      });



      // Check for missing combinations for each DCF-Entity-Period combination
      for (const dcfId of dcfIds) {
        for (const entity of entities) {
          // Extract entity ID from entity string (e.g., "3-72" -> "72")
          const entityMatch = entity.match(/^(\d+)-(\d+)$/);
          const entityId = entityMatch ? entityMatch[2] : entity;
          const level = entityMatch ? parseInt(entityMatch[1]) : 3;

          // Get actual entity name from mapping


          // for (const period of validPeriods) {
          //   const key = `${dcfId}-${level}-${entityId}-${period}`;
          //   // console.log('key2', key)
          //   // Check if this specific DCF-Entity-Period combination is missing
          //   if (!existingDataCombinations.has(key)) {
          //     // Get assignment frequency for this DCF-Entity combination
          //     const assignmentKey = `${dcfId}-${level}-${entityId}`;
          //     const assignment = assignmentInfo.get(assignmentKey);

          //     // Add individual missing data entry for each period
          //     missingData.push({
          //       dcf_id: dcfId,
          //       entity_id: entityId,
          //       entity: entityName,
          //       reporting_period: period,
          //       requested_period: period,
          //       assignment_frequency: assignment?.frequency_name || 'Unknown',
          //       reason: 'No data found for this DCF-Entity-Period combination'
          //     });
          //   }
          // }
        }
      }

      return missingData;

    } catch (error) {
      console.error('Error calculating missing data:', error);
      return [];
    }
  }


  @authenticate('cognito-jwt')
  @post('/stt-report/generate-report')
  @response(200, {
    description: 'STT Report Generation - Main entry function with complete input format',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            error_code: {
              type: 'number',
              description: 'Application-specific error code (1000-1004 for validation errors)'
            },
            error_name: {
              type: 'string',
              description: 'Error name (ValidationError, MissingFieldError, InvalidFormatError, ValueOutOfRangeError, DependencyValidationError)'
            },
            field: {
              type: 'string',
              description: 'Field name that caused the error (for validation errors)'
            },
            data: {
              type: 'object',
              description: 'Report data based on type_of_format (only present on success)'
            },
            missing_data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  dcf_id: {type: 'number'},
                  entity_id: {type: 'string'},
                  entity: {type: 'string'},
                  reporting_period: {type: 'string', description: 'The actual missing period (could be assignment-level period like "Apr-2024")'},
                  requested_period: {type: 'string', description: 'The originally requested period (like "Q1-2024")'},
                  assignment_frequency: {type: 'string', description: 'The frequency of the assignment (monthly, quarterly, etc.)'},
                  reason: {type: 'string'}
                }
              },
              description: 'Information about missing data for specific DCF-Entity-Period combinations. Shows underlying assignment periods that are missing (e.g., monthly periods for quarterly requests). The requested_period field shows what the missing period contributes to (only present on success)'
            }
          }
        }
      }
    },
  })
  async generateReport(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @requestBody({
      content: {
        'application/json': {
          required: true,
          schema: {
            type: 'object',
            properties: {
              main_data_source: {
                type: 'string',
                enum: ['quantitative', 'qualitative', 'config'],
                description: 'Main data source type'
              },
              sub_data_source: {
                type: 'string',
                enum: ['raw', 'indicator'],
                description: 'Required if main_data_source is Quantitative'
              },
              raw_parameters: {
                type: 'object',
                description: 'Required if sub_data_source is raw',
                properties: {
                  dcf_name: {
                    type: 'array',
                    items: {type: 'number'},
                    description: 'Single DCF name in array format'
                  },
                  sub_status: {
                    type: 'string',
                    enum: ['live', 'locked'],
                    description: 'live - other than draft, locked - only approved'
                  },
                  breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable breakdown'
                  },
                  breakdown_data: {
                    type: 'string',
                    enum: ['dcf_name', 'data_point_name'],
                    description: 'Required if breakdown is true'
                  }
                }
              },
              indicator_parameters: {
                type: 'object',
                description: 'Required if sub_data_source is indicator',
                properties: {
                  indicator_name: {
                    type: 'array',
                    items: {type: 'string'},
                    description: 'Single indicator name in array format'
                  },
                  sub_status: {
                    type: 'string',
                    enum: ['locked', 'breakdown'],
                    description: 'Status for indicator data'
                  },
                  breakdown_data: {
                    type: 'array',
                    items: {type: 'number'},
                    description: 'Required if sub_status is breakdown'
                  }
                }
              },
              filter_type: {
                type: 'string',
                enum: ['applied_common_filter', 'applied_specific_filter'],
                description: 'Type of filter to apply'
              },
              applied_common_filter: {
                type: 'object',
                description: 'Filters applied on the template by the user',
                properties: {
                  year: {
                    type: 'array',
                    items: {type: 'number'},
                    minItems: 1,
                    description: 'Array of years - must have exactly 1 element for direct_extract, can have multiple for queried_data. No duplicate values allowed.'
                  },
                  reporting_period: {
                    type: 'string',
                    enum: ['Monthly', 'Bi-Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom'],
                    description: 'Reporting period type'
                  },
                  reporting_period_from: {
                    type: 'string',
                    description: 'Start period (e.g., 2023-01)'
                  },
                  reporting_period_to: {
                    type: 'string',
                    description: 'End period (e.g., 2023-12)'
                  },
                  locked_date: {
                    type: 'string',
                    description: 'Locked date (e.g., 2024-01-15) or NA'
                  },
                  entity: {
                    type: 'array',
                    items: {type: 'string'},
                    minItems: 1,
                    description: 'Entity ID or Name - can have multiple elements for direct_extract, must follow queried_data rules for queried_data. Must be valid locations in format "level-id" (e.g., "1-12", "2-56", "3-72"). All entities must be at the same level - multi-level filtering is not supported (e.g., cannot mix "1-46" and "3-71"). Level 1 and Level 2 entities will be expanded to their Level 3 children. Only Level 3 entities are used for assignments and data processing. No duplicate values allowed.'
                  }
                }
              },
              applied_specific_filter: {
                type: 'object',
                description: 'Filters applied through the code',
                properties: {
                  year: {
                    type: 'array',
                    items: {type: 'number'},
                    minItems: 1,
                    description: 'Array of years - must have exactly 1 element for direct_extract, can have multiple for queried_data. No duplicate values allowed.'
                  },
                  reporting_period: {
                    type: 'string',
                    enum: ['Monthly', 'Bi-Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom'],
                    description: 'Reporting period type'
                  },
                  reporting_period_from: {
                    type: 'string',
                    description: 'Start period (e.g., 2023-01)'
                  },
                  reporting_period_to: {
                    type: 'string',
                    description: 'End period (e.g., 2023-12)'
                  },
                  locked_date: {
                    type: 'string',
                    description: 'Locked date (e.g., 2024-01-15) or NA'
                  },
                  entity: {
                    type: 'array',
                    items: {type: 'string'},
                    minItems: 1,
                    description: 'Entity ID or Name - can have multiple elements for direct_extract, must follow queried_data rules for queried_data. Must be valid locations in format "level-id" (e.g., "1-12", "2-56", "3-72"). All entities must be at the same level - multi-level filtering is not supported (e.g., cannot mix "1-46" and "3-71"). Level 1 and Level 2 entities will be expanded to their Level 3 children. Only Level 3 entities are used for assignments and data processing. No duplicate values allowed.'
                  }
                }
              },
              type_of_data: {
                type: 'string',
                enum: ['queried_data', 'direct_extract'],
                description: 'Data extraction type'
              },
              type_of_format: {
                type: 'string',
                enum: ['tabular_form_data', 'value_field', 'chart_data'],
                description: 'Output format type'
              },
              query_details: {
                type: 'object',
                description: 'Required only if type_of_data is queried_data',
                properties: {
                  query_type: {
                    type: 'string',
                    enum: ['sum', 'ratio', 'percentage', 'count'],
                    description: 'Type of query operation'
                  },
                  sub_query_type: {
                    type: 'string',
                    description: 'Sub query type (e.g., sum for entity)'
                  },
                  query_parameters: {
                    type: 'object',
                    description: 'Query parameters: filter_type, entity selected, reporting period'
                  }
                }
              },
              table_config: {
                type: 'object',
                description: 'Required only if type_of_format is tabular_form_data',
                properties: {
                  period_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable entity breakdown'
                  },
                  entity_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable entity breakdown'
                  },
                  entity_details: {
                    type: 'string',
                    description: 'Entity selected - required if entity_breakdown is true'
                  },
                  dcf_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable DCF breakdown'
                  },
                  dp_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable data point breakdown (title)'
                  }
                }
              },
              chart_config: {
                type: 'object',
                description: 'Required only if type_of_format is chart_data',
                properties: {
                  period_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable entity breakdown'
                  },
                  entity_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable entity breakdown'
                  },
                  dcf_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable DCF breakdown'
                  },
                  dp_breakdown: {
                    type: 'boolean',
                    description: 'Enable/disable data point breakdown (title)'
                  },
                  entity_details: {
                    type: 'string',
                    description: 'Entity selected - required if entity_breakdown is true'
                  }
                }
              }
            },
            required: ['main_data_source', 'filter_type', 'type_of_data', 'type_of_format']
          }
        }
      }
    })
    requestData: any): Promise<{status: boolean; message: string; error_code?: number; error_name?: string; field?: string; data?: any; missing_data?: any[]}> {
    try {
      // 1. Get user details using your pattern with cognito-jwt authentication


      const userId = currentUserProfile.email;
      if (!userId) {
        return {
          status: false,
          message: 'Unauthorized - no email found in user profile',
          error_code: 2000,
          error_name: 'UnauthorizedError'
        };
      }

      const userDetail = await this.userRepository.findOne({where: {email: userId}});
      if (!userDetail) {
        return {
          status: false,
          message: 'User not found',
          error_code: 2000,
          error_name: 'UnauthorizedError'
        };
      }

      const userProfile = await this.userProfileRepository.findOne({
        where: {userId: userDetail.id}
      });

      if (!userProfile || !userProfile.id) {
        return {
          status: false,
          message: 'User profile not found',
          error_code: 2000,
          error_name: 'UnauthorizedError'
        };
      }

      // 2. Authorization - Check clientId and roles
      const authorizationValidation = await this.validateAuthorization(userProfile.id);

      if (!authorizationValidation.isValid) {
        return authorizationValidation.error;
      }

      // 3. Extract entity access information for filtering
      const entityAccessInfo = authorizationValidation.entityAccessInfo!;

      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format,
        table_config,
        chart_config
      } = requestData;

      // Basic input validation before proceeding
      const basicValidation = this.validateBasicInputRequirements(requestData);
      if (!basicValidation.isValid) {
        return {
          status: false,
          message: basicValidation.message || 'Basic input validation failed',
          error_code: basicValidation.error_code,
          error_name: basicValidation.error_name,
          field: basicValidation.field
        };
      }

      // Entity access validation based on user role
      const activeFilterType = filter_type === 'applied_common_filter' ? applied_common_filter : filter_type === 'applied_specific_filter' ? applied_specific_filter : null;

      if (activeFilterType?.entity && Array.isArray(activeFilterType.entity)) {
        const entityAccessValidation = await this.validateEntityAccess(activeFilterType.entity, entityAccessInfo);
        if (!entityAccessValidation.isValid) {
          return entityAccessValidation.error;
        }
        // Update the filter with allowed entities only
        activeFilterType.entity = entityAccessValidation.filteredEntities!;
      }

      // Validate type_of_data specific requirements
      const yearValidation = await this.validateYearAndEntityForDataType(type_of_data, activeFilterType);
      if (!yearValidation.isValid) {
        return {
          status: false,
          message: yearValidation.message || 'Validation failed for year and entity requirements',
          error_code: yearValidation.error_code,
          error_name: yearValidation.error_name,
          field: yearValidation.field
        };
      }

      // Validate year limit
      const maxAllowedYear = 2025;
      let requestedYear: number;

      if (activeFilterType.reporting_period === 'Custom') {
        requestedYear = new Date(activeFilterType.reporting_period_from).getFullYear();
      } else {
        // Handle year as either number or array
        if (Array.isArray(activeFilterType.year)) {
          // For array, check the maximum year
          requestedYear = Math.max(...activeFilterType.year);
        } else {
          requestedYear = parseInt(activeFilterType.year);
        }
      }

      if (requestedYear > maxAllowedYear) {
        return {
          status: false,
          message: `Cannot request data for year ${requestedYear}. Maximum allowed year is ${maxAllowedYear}.`
        };
      }

      // If year is array, validate all years
      if (Array.isArray(activeFilterType.year)) {
        const invalidYears = activeFilterType.year.filter((year: number) => year > maxAllowedYear);
        if (invalidYears.length > 0) {
          return {
            status: false,
            message: `Cannot request data for years ${invalidYears.join(', ')}. Maximum allowed year is ${maxAllowedYear}.`
          };
        }
      }

      if (activeFilterType.reporting_period === 'Custom') {
        const endYear = new Date(activeFilterType.reporting_period_to).getFullYear();
        if (endYear > maxAllowedYear) {
          return {
            status: false,
            message: `Custom period end year ${endYear} exceeds maximum allowed year ${maxAllowedYear}.`
          };
        }
      }

      // Process data based on type_of_data
      let processedData: any;
      let validationResult: any;

      if (type_of_data === 'queried_data') {
        // Process queried_data and get dataset
        processedData = await this.processQueriedData(requestData, activeFilterType);
        if (!processedData.status) {
          return processedData;
        }

        // For queried_data, create a compatible structure for type_of_format processing
        validationResult = {
          status: true,
          message: processedData.message,
          valid_periods: processedData.valid_periods || [],
          data_granularity: processedData.data_granularity || 'monthly',
          dcfIds: processedData.dcfIds || []
        };

        // Calculate missing data for queried_data
        const queriedMissingDataInfo = await this.calculateMissingData(
          processedData.dcfIds || [],
          processedData.valid_periods || [],
          activeFilterType.entity,
          processedData.period_data || [],
          activeFilterType.reporting_period
        );


        // Return result for queried_data with missing_data
        const queriedFormatResult = await this.type_of_format(
          requestData.type_of_format,
          requestData.table_config,
          requestData.chart_config,
          processedData.data_sets || [],
          validationResult.valid_periods,
          activeFilterType.reporting_period,
          processedData.period_data || [],
          validationResult.dcfIds,
          activeFilterType.entity,
          sub_data_source,
          raw_parameters,
          indicator_parameters,
          type_of_data,
          activeFilterType.year,
          activeFilterType
        );

        return {
          status: true,
          message: validationResult.message || 'Report generated successfully',
          data: queriedFormatResult,
          missing_data: queriedMissingDataInfo
        };

      } else {
        // Call the validation function for direct_extract
        validationResult = await this.validateInputParameters(
          main_data_source,
          sub_data_source,
          indicator_parameters,
          raw_parameters,
          activeFilterType
        );

        if (!validationResult.status) {
          return validationResult;
        }

        // Call fetchDataFromDataLayer with validation result data
        const dataLayerResult = await this.fetchDataFromDataLayer(
          validationResult.dcfIds,
          validationResult.valid_periods,
          activeFilterType,
          sub_data_source
        );

        // Calculate data completeness status for each period
        const totalExpectedCombinations = validationResult.dcfIds.length * activeFilterType.entity.length;
        const data_complete_status = validationResult.valid_periods.map((period: string) => {
          // Find the corresponding period data from fetchedData.period_data
          const periodData = dataLayerResult.fetchedData?.period_data?.find((p: any) => p.period === period);

          if (!periodData || periodData.data_count === 0) {
            return 0; // No data found
          } else if (periodData.data_count >= totalExpectedCombinations) {
            return 2; // Complete data (has data for all DCF-entity combinations)
          } else {
            return 1; // Partial data (has some data but not complete)
          }
        });

        // Extract only the data arrays from period_data
        const period_data = dataLayerResult.fetchedData?.period_data?.map((periodObj: any) => periodObj.data) || [];

        // Calculate data_sets - sum of computedValue for each period (direct summation for direct_extract)
        const data_sets = period_data.map((periodDataArray: any[]) => {
          if (periodDataArray.length === 0) {
            return 0; // No data = 0
          }

          // Sum the computedValue field from each data item
          return periodDataArray.reduce((sum, item) => {
            // Handle different data types for computedValue
            let value = 0;
            if (item.computedValue !== null && item.computedValue !== undefined) {
              if (typeof item.computedValue === 'number') {
                value = item.computedValue;
              } else if (typeof item.computedValue === 'string') {
                // Handle common placeholder values that should be treated as 0
                const trimmedValue = item.computedValue.trim();
                if (trimmedValue === '' || trimmedValue === '-' || trimmedValue === 'N/A' || trimmedValue === 'NA') {
                  value = 0;
                } else {
                  const parsed = parseFloat(trimmedValue);
                  value = isNaN(parsed) ? 0 : parsed;
                }
              }
            }

            return sum + value;
          }, 0);
        });

        processedData = {
          status: true,
          period_data,
          data_complete_status,
          data_sets
        };
      }

      // Common type_of_format processing for both queried_data and direct_extract
      const formatResult = await this.type_of_format(
        requestData.type_of_format,
        requestData.table_config,
        requestData.chart_config,
        processedData.data_sets || [],
        validationResult.valid_periods,
        activeFilterType.reporting_period,
        processedData.period_data || [],
        validationResult.dcfIds,
        activeFilterType.entity,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        type_of_data, // Pass type_of_data to distinguish between direct_extract and queried_data
        activeFilterType.year, // Pass year for queried_data processing
        activeFilterType
      );

      // Calculate missing data information
      const missingDataInfo = await this.calculateMissingData(
        validationResult.dcfIds,
        validationResult.valid_periods,
        activeFilterType.entity,
        processedData.period_data || [],
        activeFilterType.reporting_period
      );


      // Return result with message, status, data, and missing_data
      return {
        status: true,
        message: validationResult.message || 'Report generated successfully',
        data: formatResult,
        missing_data: missingDataInfo
      };

    } catch (error) {
      return {
        status: false,
        message: `Error in report generation: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process queried_data type requests with query_details
   * Returns dataset structure compatible with type_of_format processing
   */
  private async processQueriedData(requestData: any, activeFilterType: any): Promise<any> {
    try {
      const {query_details, main_data_source, sub_data_source, indicator_parameters, raw_parameters} = requestData;

      // For now, only handle sum query_type
      if (query_details.query_type !== 'sum') {
        return {
          status: false,
          message: `query_type "${query_details.query_type}" is not yet implemented. Currently only "sum" is supported.`
        };
      }

      // Get assignment validation information for proper missing_data calculation
      const validationResult = await this.validateInputParameters(
        main_data_source,
        sub_data_source,
        indicator_parameters,
        raw_parameters,
        activeFilterType
      );

      // Process sum query and get the aggregated result
      const sumResult = await this.processSumQuery(query_details, activeFilterType, main_data_source, sub_data_source, requestData);

      if (!sumResult.status) {
        return sumResult;
      }

      // For queried_data, we need to get the actual data from fetchDataFromDataLayer
      // to provide proper entity breakdown, not just the aggregated results

      // Get the actual data layer result for proper entity/dcf breakdown
      const actualDataLayerResult = sumResult.dataLayerResult;

      // Convert the sum result to dataset format for type_of_format processing
      const dataset = this.convertSumResultToDataset(sumResult, sumResult.sub_query_type, activeFilterType);

      return {
        status: true,
        message: sumResult.message,
        data_sets: dataset.data_sets,
        period_data: actualDataLayerResult?.fetchedData?.period_data || dataset.period_data, // Use actual data if available
        data_complete_status: dataset.data_complete_status,
        valid_periods: sumResult.valid_periods || dataset.valid_periods, // Use valid_periods from sumResult
        data_granularity: dataset.data_granularity,
        dcfIds: sumResult.dcfIds || dataset.dcfIds, // Use dcfIds from sumResult, fallback to dataset
        query_result: sumResult.result, // Keep original query result for reference
        valid_assignment: validationResult.valid_assignment || [] // Include assignment information for missing_data calculation
      };

    } catch (error) {
      return {
        status: false,
        message: `Error processing queried data: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Convert sum query result to dataset format compatible with type_of_format processing
   */
  private convertSumResultToDataset(sumResult: any, sub_query_type: string, activeFilterType: any): any {
    const result = sumResult.result;
    let data_sets: number[] = [];
    let period_data: any[] = [];
    let valid_periods: string[] = [];
    let data_complete_status: number[] = [];

    switch (sub_query_type) {
      case 'sum_for_entity_for_periods':
        // Extract period data from entity results
        const entityKeys = Object.keys(result);
        if (entityKeys.length > 0) {
          const firstEntity = result[entityKeys[0]];
          valid_periods = Object.keys(firstEntity.period_breakdown || {});

          // Create data_sets as sum across all entities for each period
          data_sets = valid_periods.map(period => {
            return entityKeys.reduce((sum, entityKey) => {
              return sum + (result[entityKey].period_breakdown[period] || 0);
            }, 0);
          });
        }
        break;

      case 'sum_for_all_entities_by_period':
        // Extract from period_totals
        valid_periods = Object.keys(result.period_totals || {});
        data_sets = valid_periods.map(period => result.period_totals[period] || 0);
        break;

      case 'breakdown_by_entity':
        // Extract periods from first entity's period_totals
        const entityBreakdownKeys = Object.keys(result.entity_breakdown || {});
        if (entityBreakdownKeys.length > 0) {
          const firstEntityBreakdown = result.entity_breakdown[entityBreakdownKeys[0]];
          valid_periods = Object.keys(firstEntityBreakdown.period_totals || {});

          // Sum across all entities for each period
          data_sets = valid_periods.map(period => {
            return entityBreakdownKeys.reduce((sum, entityKey) => {
              return sum + (result.entity_breakdown[entityKey].period_totals[period] || 0);
            }, 0);
          });
        }
        break;

      case 'breakdown_by_period':
        // Extract from period_breakdown
        valid_periods = Object.keys(result.period_breakdown || {});
        data_sets = valid_periods.map(period => result.period_breakdown[period]?.period_total || 0);
        break;

      default:
        // Fallback
        data_sets = [0];
        valid_periods = ['Unknown'];
    }

    // Create period_data structure (simplified for now)
    period_data = data_sets.map((value, index) => [{computedValue: value, period: valid_periods[index]}]);

    // Assume complete data for now
    data_complete_status = data_sets.map(() => 2);

    return {
      data_sets,
      period_data,
      valid_periods,
      data_complete_status,
      data_granularity: this.determineDataGranularity(valid_periods),
      dcfIds: sumResult.dcfIds || [] // Use dcfIds from sumResult
    };
  }

  /**
   * Determine data granularity from valid periods
   */
  private determineDataGranularity(valid_periods: string[]): string {
    if (valid_periods.length === 0) return 'monthly';

    const firstPeriod = valid_periods[0];
    if (firstPeriod.includes('Q')) return 'quarterly';
    if (firstPeriod.includes('H')) return 'half-yearly';
    if (firstPeriod.includes('BM')) return 'bi-monthly';
    if (firstPeriod.match(/^\d{4}$/)) return 'yearly';
    return 'monthly';
  }

  /**
   * Process sum query type with different sub_query_types
   * 1. Identify Query Type and Sub-Type from query_details
   * 2. Extract parameters from activeFilterType (year, reporting_period, entity)
   * 3. Get indicator_name/dcf_name from raw_parameters/indicator_parameters
   * 4. Fetch data using fetchDataFromDataLayer()
   * 5. Process based on sub_query_type logic
   */
  private async processSumQuery(query_details: any, activeFilterType: any, main_data_source: string, sub_data_source: string, requestData?: any): Promise<any> {
    try {
      // 1. Identify Query Type and Sub-Type
      const {sub_query_type, query_parameters} = query_details;
      const query_type = query_details.query_type; // Should be 'sum'

      // Default sub_query_type if not provided
      const effectiveSubQueryType = sub_query_type || 'sum_for_entity_for_periods';

      // 2. Extract parameters from activeFilterType
      const year = activeFilterType.year; // Array of years
      const reporting_period = activeFilterType.reporting_period;
      const entity = activeFilterType.entity; // Array of entity names/IDs

      // 3. Get indicator_name or dcf_name based on sub_data_source and request data
      let dcfIds: number[] = [];
      let dataSource: 'dcf' | 'indicator' = 'dcf';

      if (sub_data_source === 'raw' && requestData?.raw_parameters?.dcf_name) {
        // Use DCF names from raw_parameters
        dcfIds = requestData.raw_parameters.dcf_name.map((id: any) => parseInt(id));
        dataSource = 'dcf';
      } else if (sub_data_source === 'indicator' && requestData?.indicator_parameters?.indicator_name) {
        // Use indicator names and resolve to DCF IDs

        dcfIds = Array.from(new Set(this.hardcodedSTTIndicatorList.filter(x => requestData?.indicator_parameters?.indicator_name.includes(x.id)).flatMap((x: any) => x.dcfIds || []))) as number[];

        dataSource = 'indicator';
      } else {
        return {
          status: false,
          message: `Unable to determine ${sub_data_source === 'raw' ? 'DCF names' : 'indicator names'} from request data. Please ensure ${sub_data_source === 'raw' ? 'raw_parameters' : 'indicator_parameters'} contain the required data.`
        };
      }

      if (dcfIds.length === 0) {
        return {
          status: false,
          message: `No valid DCF IDs found for the specified ${dataSource} data`
        };
      }

      // 4. Calculate valid periods based on year and reporting_period
      const validPeriods = this.calculateValidPeriodsForQuery(year, reporting_period);

      // 5. Fetch Data using fetchDataFromDataLayer
      const dataLayerResult = await this.fetchDataFromDataLayer(
        dcfIds,
        validPeriods,
        activeFilterType,
        sub_data_source
      );

      // 6. Process based on sub_query_type logic
      const queryResult = await this.processQuerySubType(
        effectiveSubQueryType,
        dataLayerResult,
        entity,
        validPeriods,
        {
          query_type,
          year,
          reporting_period,
          dcfIds,
          dataSource
        }
      );

      return {
        status: true,
        message: `${query_type} query with ${effectiveSubQueryType} processed successfully`,
        query_type,
        sub_query_type: effectiveSubQueryType,
        result: queryResult,
        dcfIds,
        valid_periods: validPeriods,
        data_granularity: this.determineDataGranularity(validPeriods),
        dataLayerResult // Include the actual data layer result for entity breakdown
      };

    } catch (error) {
      return {
        status: false,
        message: `Error processing sum query: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Calculate valid periods for query based on year and reporting_period
   */
  private calculateValidPeriodsForQuery(year: number[], reporting_period: string): string[] {
    const periods: string[] = [];

    year.forEach(yearValue => {
      switch (reporting_period.toLowerCase()) {
        case 'monthly':
          for (let month = 1; month <= 12; month++) {
            periods.push(`${month.toString().padStart(2, '0')}-${yearValue}`);
          }
          break;
        case 'quarterly':
          periods.push(`Q1-${yearValue}`, `Q2-${yearValue}`, `Q3-${yearValue}`, `Q4-${yearValue}`);
          break;
        case 'bi-monthly':
          for (let bm = 1; bm <= 6; bm++) {
            periods.push(`BM${bm}-${yearValue}`);
          }
          break;
        case 'half-yearly':
          periods.push(`H1-${yearValue}`, `H2-${yearValue}`);
          break;
        case 'yearly':
          periods.push(yearValue.toString());
          break;
        default:
          periods.push(`${yearValue}`);
      }
    });

    return periods;
  }

  /**
   * Process query based on sub_query_type
   */
  private async processQuerySubType(
    sub_query_type: string,
    dataLayerResult: any,
    entities: string[],
    validPeriods: string[],
    queryContext: any
  ): Promise<any> {
    switch (sub_query_type) {
      case 'sum_for_entity_for_periods':
        return this.processSumForEntityForPeriods(dataLayerResult, entities, queryContext);

      case 'sum_for_all_entities_by_period':
        return this.processSumForAllEntitiesByPeriod(dataLayerResult, entities, queryContext);

      case 'breakdown_by_entity':
        return this.processBreakdownByEntity(dataLayerResult, entities, queryContext);

      case 'breakdown_by_period':
        return this.processBreakdownByPeriod(dataLayerResult, entities, queryContext);

      default:
        throw new Error(`sub_query_type "${sub_query_type}" is not implemented`);
    }
  }

  /**
   * Extract entity name from data item
   */
  private extractEntityName(dataItem: any): string {
    // Try different possible entity name fields
    return dataItem.entityName || dataItem.entity_name || dataItem.locationName ||
      dataItem.location_name || dataItem.name || `${dataItem.level}-${dataItem.locationId}`;
  }

  /**
   * Check if data item matches entity format (name or ID format)
   */
  private matchesEntityFormat(dataItem: any, entityName: string): boolean {
    // Check if entityName matches the level-locationId format
    if (entityName.includes('-')) {
      const [level, locationId] = entityName.split('-');
      return dataItem.level?.toString() === level && dataItem.locationId?.toString() === locationId;
    }

    // Check various entity name fields
    const itemEntityName = this.extractEntityName(dataItem);
    return itemEntityName === entityName;
  }

  /**
   * Process sum_for_entity_for_periods sub_query_type
   * Sum all values for the specified entity and selected periods (year specifically)
   */
  private processSumForEntityForPeriods(dataLayerResult: any, entities: string[], queryContext: any): any {
    const result: any = {};

    // Process each entity separately
    entities.forEach(entityName => {
      let entityTotalSum = 0;
      const periodBreakdown: any = {};

      // Process period data if available
      if (dataLayerResult.fetchedData?.period_data) {
        dataLayerResult.fetchedData.period_data.forEach((periodObj: any) => {
          const periodData = periodObj.data || []; // Array of data items
          let periodSum = 0;

          // Filter data for this specific entity and sum values
          periodData.forEach((dataItem: any) => {
            // Match entity by level-locationId format or entity name
            const itemEntityId = `${dataItem.level}-${dataItem.locationId}`;
            const itemEntityName = this.extractEntityName(dataItem);

            if (itemEntityId === entityName || itemEntityName === entityName ||
              this.matchesEntityFormat(dataItem, entityName)) {
              const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
              periodSum += value;
            }
          });

          // Store period breakdown
          const periodLabel = periodObj.period;
          periodBreakdown[periodLabel] = periodSum;
          entityTotalSum += periodSum;
        });
      }

      result[entityName] = {
        total_sum: entityTotalSum,
        period_breakdown: periodBreakdown
      };
    });

    return result;
  }

  /**
   * Process sum_for_all_entities_by_period sub_query_type
   * Fetch data for all selected entities for the given period, then compute the total sum
   */
  private processSumForAllEntitiesByPeriod(dataLayerResult: any, entities: string[], queryContext: any): any {
    const result: any = {
      period_totals: {},
      grand_total: 0
    };

    // Process each period separately
    if (dataLayerResult.fetchedData?.period_data) {
      dataLayerResult.fetchedData.period_data.forEach((periodObj: any) => {
        const periodData = periodObj.data || [];
        let periodTotal = 0;

        // Sum all values for all selected entities in this period
        periodData.forEach((dataItem: any) => {
          const itemEntityId = `${dataItem.level}-${dataItem.locationId}`;
          const itemEntityName = this.extractEntityName(dataItem);

          // Check if this data item belongs to any of the selected entities
          const belongsToSelectedEntity = entities.some(entityName =>
            itemEntityId === entityName || itemEntityName === entityName ||
            this.matchesEntityFormat(dataItem, entityName)
          );

          if (belongsToSelectedEntity) {
            const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
            periodTotal += value;
          }
        });

        const periodLabel = periodObj.period;
        result.period_totals[periodLabel] = periodTotal;
        result.grand_total += periodTotal;
      });
    }

    return result;
  }

  /**
   * Process breakdown_by_entity sub_query_type
   * Repeat the sum logic for each entity separately, and return a breakdown (e.g., row-wise table)
   */
  private processBreakdownByEntity(dataLayerResult: any, entities: string[], queryContext: any): any {
    const result: any = {
      entity_breakdown: {},
      summary: {
        total_entities: entities.length,
        grand_total: 0
      }
    };

    // Process each entity separately
    entities.forEach(entityName => {
      let entityTotal = 0;
      const entityDetails: any[] = [];
      const periodTotals: any = {};

      // Process period data for this entity
      if (dataLayerResult.fetchedData?.period_data) {
        dataLayerResult.fetchedData.period_data.forEach((periodObj: any) => {
          const periodData = periodObj.data || [];
          let periodSum = 0;
          const periodDetails: any[] = [];

          // Filter and sum data for this specific entity
          periodData.forEach((dataItem: any) => {
            const itemEntityId = `${dataItem.level}-${dataItem.locationId}`;
            const itemEntityName = this.extractEntityName(dataItem);

            if (itemEntityId === entityName || itemEntityName === entityName ||
              this.matchesEntityFormat(dataItem, entityName)) {
              const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
              periodSum += value;

              // Store individual data point details
              periodDetails.push({
                dcf_id: dataItem.dcfId,
                value: value,
                period: periodObj.period,
                data_point: dataItem.dataPoint || dataItem.indicator || 'Unknown',
                entity_id: itemEntityId,
                entity_name: itemEntityName
              });
            }
          });

          const periodLabel = periodObj.period;
          periodTotals[periodLabel] = periodSum;
          entityTotal += periodSum;

          if (periodDetails.length > 0) {
            entityDetails.push({
              period: periodLabel,
              period_total: periodSum,
              data_points: periodDetails
            });
          }
        });
      }

      result.entity_breakdown[entityName] = {
        total: entityTotal,
        period_totals: periodTotals,
        details: entityDetails
      };

      result.summary.grand_total += entityTotal;
    });

    return result;
  }

  /**
   * Process breakdown_by_period sub_query_type
   * Repeat the sum logic across each time unit in the period (e.g., Jan–Dec)
   */
  private processBreakdownByPeriod(dataLayerResult: any, entities: string[], queryContext: any): any {
    const result: any = {
      period_breakdown: {},
      summary: {
        total_periods: 0,
        grand_total: 0
      }
    };

    // Process each period separately
    if (dataLayerResult.fetchedData?.period_data) {
      dataLayerResult.fetchedData.period_data.forEach((periodObj: any) => {
        const periodData = periodObj.data || [];
        const periodLabel = periodObj.period;

        let periodTotal = 0;
        const entityBreakdown: any = {};

        // Process each entity within this period
        entities.forEach(entityName => {
          let entitySum = 0;
          const entityDataPoints: any[] = [];

          // Filter and sum data for this entity in this period
          periodData.forEach((dataItem: any) => {
            const itemEntityId = `${dataItem.level}-${dataItem.locationId}`;
            const itemEntityName = this.extractEntityName(dataItem);

            if (itemEntityId === entityName || itemEntityName === entityName ||
              this.matchesEntityFormat(dataItem, entityName)) {
              const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
              entitySum += value;

              entityDataPoints.push({
                dcf_id: dataItem.dcfId,
                value: value,
                data_point: dataItem.dataPoint || dataItem.indicator || 'Unknown',
                entity_id: itemEntityId,
                entity_name: itemEntityName
              });
            }
          });

          if (entitySum > 0 || entityDataPoints.length > 0) {
            entityBreakdown[entityName] = {
              total: entitySum,
              data_points: entityDataPoints
            };
          }

          periodTotal += entitySum;
        });

        // Store period breakdown
        result.period_breakdown[periodLabel] = {
          period_total: periodTotal,
          entity_breakdown: entityBreakdown,
          entities_count: Object.keys(entityBreakdown).length
        };

        result.summary.grand_total += periodTotal;
        result.summary.total_periods++;
      });
    }

    return result;
  }

  /**
   * Validate input parameters for STT report generation
   * This function performs all validation logic and returns the validation result
   */
  private async validateInputParameters(
    main_data_source: string,
    sub_data_source: string,
    indicator_parameters: any,
    raw_parameters: any,
    activeFilterType: any
  ): Promise<any> {
    try {
      // Process assignments if quantitative data source
      if (main_data_source === 'quantitative') {
        let dcfIds: number[] = [];

        if (sub_data_source === 'raw') {
          dcfIds = raw_parameters?.dcf_name || [];
        } else if (sub_data_source === 'indicator') {
          if (!indicator_parameters) {
            return {
              status: false,
              message: 'Indicator parameters are required for indicator data source',
              valid_periods: [],
              data_granularity: null,
              valid_assignment: [],
              dcfIds
            };
          }


          const dcfId_list = Array.from(new Set(this.hardcodedSTTIndicatorList.filter(x => indicator_parameters?.indicator_name.includes(x.id)).flatMap((x: any) => x.dcfIds || []))) as number[];
          dcfIds = indicator_parameters?.breakdown ?
            dcfId_list.filter((x: number) => indicator_parameters?.breakdown_data?.includes(x)) :
            dcfId_list;

        }

        if (dcfIds.length === 0) {
          return {
            status: false,
            message: 'No valid DCF IDs found for processing',
            valid_periods: [],
            data_granularity: null,
            valid_assignment: [], dcfIds
          };
        }

        // Get assignments
        const dcfAssignment = await this.userProfileController.getAssignedIndicatorList(94, {
          where: {
            dcfId: {inq: dcfIds},
            locationId: {inq: activeFilterType.entity.map((entity: any) => entity.split('-')[1]).map((id: any) => parseInt(id)) || []}
          }
        }) as DcfAssignment[];

        if (!dcfAssignment || dcfAssignment.length === 0) {
          return {
            status: false,
            message: 'No valid assignments found for the given input',
            valid_periods: [],
            data_granularity: null,
            valid_assignment: [], dcfIds
          };
        }

        // Filter assignments based on requested period
        let periodStart: Date;
        let periodEnd: Date;

        if (activeFilterType.reporting_period === 'Custom') {
          periodStart = new Date(activeFilterType.reporting_period_from);
          periodEnd = new Date(activeFilterType.reporting_period_to);
        } else {
          // Handle year as either number or array
          let yearValue: number;
          if (Array.isArray(activeFilterType.year)) {
            // For array, use the minimum year for start and maximum year for end
            const minYear = Math.min(...activeFilterType.year);
            const maxYear = Math.max(...activeFilterType.year);
            periodStart = new Date(minYear.toString() + '-01-01');
            periodEnd = new Date(maxYear.toString() + '-12-31');
          } else {
            yearValue = activeFilterType.year;
            periodStart = new Date(yearValue.toString() + '-01-01');
            periodEnd = new Date(yearValue.toString() + '-12-31');
          }
        }

        // Set times to start and end of day for accurate comparison
        periodStart.setUTCHours(0, 0, 0, 0);
        periodEnd.setUTCHours(23, 59, 59, 999);

        const validPeriodAssignments = dcfAssignment.filter(assignment => {
          const assignmentStart = new Date(assignment.start_date);
          const assignmentEnd = assignment.end_date ? new Date(assignment.end_date) : new Date();

          assignmentStart.setUTCHours(0, 0, 0, 0);
          assignmentEnd.setUTCHours(23, 59, 59, 999);

          return assignmentStart <= periodEnd && assignmentEnd >= periodStart;
        });

        if (validPeriodAssignments.length === 0) {
          return {
            status: false,
            message: 'No valid assignments found for the requested period',
            valid_periods: [], periodStart, periodEnd,
            data_granularity: null,
            valid_assignment: [], dcfIds
          };
        }

        // Create a map to track assignments by entity and DCF
        const assignmentMap = new Map<string, any>();
        const frequencyMapping: {[key: number]: string} = {
          1: 'monthly',
          2: 'bi-monthly',
          3: 'quarterly',
          4: 'yearly',
          5: 'half-yearly'
        };

        // Group assignments by entity-dcf combination and validate frequency
        const validAssignments: any[] = [];
        const invalidAssignments: any[] = [];

        for (const assignment of validPeriodAssignments) {
          const entity = `${assignment.level}-${assignment.locationId}`;
          const key = `${assignment.dcfId}-${entity}`;

          // Check frequency compatibility using resolveReportingFrequency
          const frequencyResult = this.resolveReportingFrequency(
            {
              reporting_frequency: this.getFrequencyFromCode(assignment.frequency),
              reporting_period_from: activeFilterType.reporting_period_from,
              reporting_period: activeFilterType.reporting_period,
              reporting_period_to: activeFilterType.reporting_period_to,
              year: activeFilterType.year, entity: activeFilterType.entity
            },
            assignment
          );

          const assignmentResult: any = {
            dcfId: assignment.dcfId,
            entity,
            locationId: assignment.locationId,
            combination: key,
            assignment,
            frequencyResult,
            isValid: false,
            errors: [],
            assignmentDetails: {
              dcfId: assignment.dcfId,
              locationId: assignment.locationId,
              frequency: assignment.frequency,
              frequencyName: frequencyMapping[assignment.frequency],
              start_date: assignment.start_date,
              end_date: assignment.end_date,
              level: assignment.level
            }
          };

          if (!frequencyResult.isValid) {
            assignmentResult.isValid = false;
            assignmentResult.errors = [`Frequency incompatibility: ${frequencyResult.error}`];
            invalidAssignments.push(assignmentResult);
          } else {
            assignmentResult.isValid = true;
            assignmentResult.errors = [];
            assignmentResult.valid_periods = frequencyResult.periods?.valid_periods || [];
            assignmentResult.data_granularity = frequencyResult.periods?.data_granularity;
            assignmentResult.reportingPeriods = frequencyResult.periods;
            validAssignments.push(assignmentResult);
            assignmentMap.set(key, assignmentResult);
          }
        }

        // Check if we have any invalid assignments
        if (invalidAssignments.length > 0) {
          const errorMessages = invalidAssignments.map(invalid =>
            `DCF: ${invalid.dcfId}, Entity: ${invalid.entity} - ${invalid.errors[0]}`
          ).join('\n');

          return {
            status: false,
            message: `[VALIDATION ERROR] ${invalidAssignments.length} out of ${invalidAssignments.length + validAssignments.length} assignments have frequency incompatibility:\n${errorMessages}`,
            valid_periods: [], periodStart, periodEnd,
            data_granularity: null,
            valid_assignment: validAssignments,
            dcfIds
          };
        }

        // All assignments are valid - prepare response
        const validAssignmentsArray = Array.from(assignmentMap.values());

        // Transform validAssignments to include individual valid_periods for each assignment
        const validAssignmentsWithPeriods = validAssignmentsArray.map(assignment => ({
          ...assignment,
          valid_periods: assignment.reportingPeriods?.valid_periods || [],
          data_granularity: assignment.reportingPeriods?.data_granularity
        }));

        // Get valid_periods and data_granularity from the first assignment
        const valid_periods = validAssignmentsArray[0]?.reportingPeriods?.valid_periods || [];
        const data_granularity = validAssignmentsArray[0]?.reportingPeriods?.data_granularity || 'monthly';

        // Create frequency summary for the message
        const frequencies = validAssignments.map(v => v.assignment.frequency);
        const uniqueFrequencies = [...new Set(frequencies)];
        const frequencySummary = uniqueFrequencies.length === 1
          ? `All assignments have ${frequencyMapping[uniqueFrequencies[0]]} frequency.`
          : `Mixed frequencies: ${uniqueFrequencies.map(f => frequencyMapping[f]).join(', ')}. All can support ${activeFilterType.reporting_period} reporting.`;

        return {
          status: true,
          message: `All ${dcfIds.length * activeFilterType.entity.length} DCF-entity combinations have valid assignments. ${frequencySummary}`,
          valid_periods, dcfIds, periodStart, periodEnd,
          data_granularity,
          valid_assignment: validAssignmentsWithPeriods
        };
      }

      // If not quantitative, just return success for now
      return {
        status: true,
        message: 'Successfully generated periods',
        valid_periods: [],
        data_granularity: 'monthly',
        valid_assignment: []
      };

    } catch (error) {
      return {
        status: false,
        message: `Error validating input parameters: ${error instanceof Error ? error.message : 'Unknown error'}`,
        valid_periods: [],
        data_granularity: null,
        valid_assignment: []
      };
    }
  }

  /**
   * Load form configuration for the given DCF IDs
   * Fetches DCF details from formCollectionRepository and returns mapped structure
   */
  private async loadFormConfiguration(dcfIds: number[]): Promise<any> {
    try {
      // Get DCF forms from formCollectionRepository
      const dcfForms = await this.formCollectionRepository.find({
        where: {
          id: {inq: dcfIds}
        },
        fields: ['id', 'title']
      });

      // Map to the required structure
      const mapped_dcf_forms = dcfForms.map(form => ({
        dcf_name: form.title,
        data_points: [] // Keep empty array for now as requested
      }));

      return {
        mapped_dcf_forms
      };

    } catch (error) {
      console.error('Error loading form configuration:', error);
      return {
        mapped_dcf_forms: []
      };
    }
  }

  /**
   * Fetch data from data layer based on DCF IDs, valid periods, entity, and data source
   */
  private async fetchDataFromDataLayer(
    _dcfIds: number[],
    valid_periods: string[],
    activeFilterType: any,
    _sub_data_source: string
  ): Promise<any> {
    try {
      // Calculate startMonth and endMonth from valid_periods
      let startMonth: string;
      let endMonth: string;
      const entity = activeFilterType?.entity || '';
      if (valid_periods.length > 0) {
        // Determine period format and extract start/end months
        const firstPeriod = valid_periods[0];
        const lastPeriod = valid_periods[valid_periods.length - 1];

        if (firstPeriod.includes('-') && firstPeriod.match(/^\d{2}-\d{4}$/)) {
          // Monthly format: "01-2024"
          const [startMonthNum, startYear] = firstPeriod.split('-');
          const [endMonthNum, endYear] = lastPeriod.split('-');
          startMonth = `${this.getMonthName(parseInt(startMonthNum))}-${startYear}`;
          endMonth = `${this.getMonthName(parseInt(endMonthNum))}-${endYear}`;
        } else if (firstPeriod.includes('Q')) {
          // Quarterly format: "Q1-2025"
          const [startQ, startYear] = firstPeriod.split('-');
          const [endQ, endYear] = lastPeriod.split('-');
          startMonth = `${this.getQuarterStartMonth(startQ)}-${startYear}`;
          endMonth = `${this.getQuarterEndMonth(endQ)}-${endYear}`;
        } else if (firstPeriod.includes('H')) {
          // Half-yearly format: "H1-2025"
          const [startH, startYear] = firstPeriod.split('-');
          const [endH, endYear] = lastPeriod.split('-');
          startMonth = `${this.getHalfYearStartMonth(startH)}-${startYear}`;
          endMonth = `${this.getHalfYearEndMonth(endH)}-${endYear}`;
        } else if (firstPeriod.includes('BM')) {
          // Bi-monthly format: "BM1-2025"
          const [startBM, startYear] = firstPeriod.split('-');
          const [endBM, endYear] = lastPeriod.split('-');
          startMonth = `${this.getBiMonthlyStartMonth(startBM)}-${startYear}`;
          endMonth = `${this.getBiMonthlyEndMonth(endBM)}-${endYear}`;
        } else {
          // Yearly format: "2025"
          startMonth = `Jan-${firstPeriod}`;
          endMonth = `Dec-${lastPeriod}`;
        }
      } else {
        // Fallback to current year
        const currentYear = new Date().getFullYear();
        startMonth = `Jan-${currentYear}`;
        endMonth = `Dec-${currentYear}`;
      }

      // Get indicator data approval indicators using client ID 94
      const dataPoints = await this.userProfileController.getIndicatorDataApprovalIndicatorsSTT(94, {
        indicators: _sub_data_source === 'indicator' ? activeFilterType?.indicator_parameters?.indicator_name : undefined,
        year: {startMonth, endMonth}
      });
      // Filter by entity
      const entityFilteredDataPoints = dataPoints.filter((x: any) =>
        entity?.includes(x.level + "-" + x.locationId) && _dcfIds.includes(x.dcfId)
      );

      // Determine data granularity from valid_periods format
      let dataGranularity: string;
      if (valid_periods.length > 0) {
        const firstPeriod = valid_periods[0];
        if (firstPeriod.includes('Q')) {
          dataGranularity = 'quarterly';
        } else if (firstPeriod.includes('H')) {
          dataGranularity = 'half-yearly';
        } else if (firstPeriod.includes('BM')) {
          dataGranularity = 'bi-monthly';
        } else if (firstPeriod.match(/^\d{4}$/)) {
          dataGranularity = 'yearly';
        } else {
          dataGranularity = 'monthly';
        }
      } else {
        dataGranularity = 'monthly';
      }



      // Call appropriate fetch function based on data granularity
      let fetchedData: any;
      switch (dataGranularity) {
        case 'monthly':
          fetchedData = await this.fetchMonthlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'bi-monthly':
          fetchedData = await this.fetchBiMonthlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'quarterly':
          fetchedData = await this.fetchQuarterlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'half-yearly':
          fetchedData = await this.fetchHalfYearlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'yearly':
          fetchedData = await this.fetchYearlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        default:
          fetchedData = await this.fetchMonthlyDcfData(entityFilteredDataPoints, valid_periods);
      }

      return {
        dataPoints: entityFilteredDataPoints,
        fetchedData, act: dataPoints,
        dataGranularity,
        period: {
          startMonth,
          endMonth
        }
      };

    } catch (error) {
      console.error('Error fetching data from data layer:', error);
      return {
        dataPoints: [],
        fetchedData: null,
        dataGranularity: 'monthly',
        period: {
          startMonth: '',
          endMonth: ''
        }
      };
    }
  }

  // Helper methods for period conversion
  private getMonthName(monthNum: number): string {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[monthNum - 1] || 'Jan';
  }

  /**
   * Determine if we should use requested period granularity instead of assignment frequency granularity
   * for missing data calculation
   */
  private shouldUseRequestedPeriodGranularity(requestedReportingPeriod: string): boolean {
    // Use requested period granularity when the requested period is less granular than monthly
    const requestedPeriod = requestedReportingPeriod?.toLowerCase();
    return requestedPeriod === 'half-yearly' || requestedPeriod === 'quarterly' || requestedPeriod === 'yearly';
  }

  /**
   * Filter assignment periods to only include those within the requested reporting period
   */
  private filterAssignmentPeriodsForRequest(
    assignmentPeriods: string[],
    requestedReportingPeriod: string,
    assignmentFrequency: string
  ): string[] {
    if (!requestedReportingPeriod) {
      return assignmentPeriods; // Return all periods if no specific period requested
    }

    const normalizedRequestedPeriod = requestedReportingPeriod.toLowerCase();
    const normalizedAssignmentFreq = assignmentFrequency.toLowerCase();

    // If assignment frequency matches requested period, return all assignment periods
    if (normalizedAssignmentFreq === normalizedRequestedPeriod) {
      return assignmentPeriods;
    }

    // Check frequency compatibility using existing logic
    const compatibilityMatrix: {[key: string]: string[]} = {
      'monthly': ['monthly', 'bi-monthly', 'quarterly', 'half-yearly', 'yearly'],
      'bi-monthly': ['bi-monthly', 'half-yearly', 'yearly'],
      'quarterly': ['quarterly', 'half-yearly', 'yearly'],
      'half-yearly': ['half-yearly', 'yearly'],
      'yearly': ['yearly'],
      'custom': ['monthly', 'bi-monthly', 'quarterly', 'half-yearly', 'yearly', 'custom']
    };

    const canSupport = compatibilityMatrix[normalizedAssignmentFreq]?.includes(normalizedRequestedPeriod) || false;

    if (!canSupport) {
      // If assignment frequency cannot support requested period, return empty array
      return [];
    }

    // For compatible frequencies, filter assignment periods based on requested reporting period
    switch (normalizedRequestedPeriod) {
      case 'monthly':
        return assignmentPeriods; // Monthly can show all assignment periods

      case 'bi-monthly':
        // Filter to show only bi-monthly compatible periods
        return this.filterPeriodsForBiMonthly(assignmentPeriods, normalizedAssignmentFreq);

      case 'quarterly':
        // Filter to show only quarterly compatible periods
        return this.filterPeriodsForQuarterly(assignmentPeriods, normalizedAssignmentFreq);

      case 'half-yearly':
        // Filter to show only half-yearly compatible periods
        return this.filterPeriodsForHalfYearly(assignmentPeriods, normalizedAssignmentFreq);

      case 'yearly':
        // Filter to show only yearly compatible periods
        return this.filterPeriodsForYearly(assignmentPeriods, normalizedAssignmentFreq);

      case 'custom':
        return assignmentPeriods; // Custom can show all assignment periods

      default:
        return assignmentPeriods; // Default: return all periods
    }
  }

  /**
   * Filter assignment periods for bi-monthly reporting
   */
  private filterPeriodsForBiMonthly(assignmentPeriods: string[], assignmentFrequency: string): string[] {
    if (assignmentFrequency === 'monthly') {
      // For monthly assignments, group into bi-monthly periods
      return assignmentPeriods.filter(period => {
        // Keep periods that align with bi-monthly boundaries (Jan-Feb, Mar-Apr, May-Jun, etc.)
        const monthMatch = period.match(/^(\d{2})-(\d{4})$/);
        if (monthMatch) {
          const month = parseInt(monthMatch[1]);
          return month % 2 === 1; // Keep odd months (1, 3, 5, 7, 9, 11) as bi-monthly start periods
        }
        return true; // Keep non-monthly format periods
      });
    }
    return assignmentPeriods; // For other frequencies, return all periods
  }

  /**
   * Filter assignment periods for quarterly reporting
   */
  private filterPeriodsForQuarterly(assignmentPeriods: string[], assignmentFrequency: string): string[] {
    if (assignmentFrequency === 'monthly') {
      // For monthly assignments, keep only quarter start months (Jan, Apr, Jul, Oct)
      return assignmentPeriods.filter(period => {
        const monthMatch = period.match(/^(\d{2})-(\d{4})$/);
        if (monthMatch) {
          const month = parseInt(monthMatch[1]);
          return [1, 4, 7, 10].includes(month); // Q1, Q2, Q3, Q4 start months
        }
        return true; // Keep non-monthly format periods
      });
    } else if (assignmentFrequency === 'bi-monthly') {
      // For bi-monthly assignments, keep periods that align with quarters
      return assignmentPeriods.filter(period => {
        const biMonthMatch = period.match(/^BM(\d+)-(\d{4})$/);
        if (biMonthMatch) {
          const biMonth = parseInt(biMonthMatch[1]);
          return [1, 3, 5].includes(biMonth); // BM1 (Jan-Feb), BM3 (May-Jun), BM5 (Sep-Oct) partially align
        }
        return true; // Keep non-bi-monthly format periods
      });
    }
    return assignmentPeriods; // For other frequencies, return all periods
  }

  /**
   * Filter assignment periods for half-yearly reporting
   */
  private filterPeriodsForHalfYearly(assignmentPeriods: string[], assignmentFrequency: string): string[] {
    if (assignmentFrequency === 'monthly') {
      // For monthly assignments, keep only half-year start months (Jan, Jul)
      return assignmentPeriods.filter(period => {
        const monthMatch = period.match(/^(\d{2})-(\d{4})$/);
        if (monthMatch) {
          const month = parseInt(monthMatch[1]);
          return [1, 7].includes(month); // H1, H2 start months
        }
        return true; // Keep non-monthly format periods
      });
    } else if (assignmentFrequency === 'bi-monthly') {
      // For bi-monthly assignments, keep periods that align with half-years
      return assignmentPeriods.filter(period => {
        const biMonthMatch = period.match(/^BM(\d+)-(\d{4})$/);
        if (biMonthMatch) {
          const biMonth = parseInt(biMonthMatch[1]);
          return [1, 4].includes(biMonth); // BM1 (Jan-Feb), BM4 (Jul-Aug) start each half
        }
        return true; // Keep non-bi-monthly format periods
      });
    } else if (assignmentFrequency === 'quarterly') {
      // For quarterly assignments, keep periods that align with half-years
      return assignmentPeriods.filter(period => {
        const quarterMatch = period.match(/^Q(\d+)-(\d{4})$/);
        if (quarterMatch) {
          const quarter = parseInt(quarterMatch[1]);
          return [1, 3].includes(quarter); // Q1, Q3 start each half
        }
        return true; // Keep non-quarterly format periods
      });
    }
    return assignmentPeriods; // For other frequencies, return all periods
  }

  /**
   * Filter assignment periods for yearly reporting
   */
  private filterPeriodsForYearly(assignmentPeriods: string[], assignmentFrequency: string): string[] {
    if (assignmentFrequency === 'monthly') {
      // For monthly assignments, keep only year start month (Jan)
      return assignmentPeriods.filter(period => {
        const monthMatch = period.match(/^(\d{2})-(\d{4})$/);
        if (monthMatch) {
          const month = parseInt(monthMatch[1]);
          return month === 1; // January only
        }
        return true; // Keep non-monthly format periods
      });
    } else if (assignmentFrequency === 'bi-monthly') {
      // For bi-monthly assignments, keep only year start period
      return assignmentPeriods.filter(period => {
        const biMonthMatch = period.match(/^BM(\d+)-(\d{4})$/);
        if (biMonthMatch) {
          const biMonth = parseInt(biMonthMatch[1]);
          return biMonth === 1; // BM1 (Jan-Feb) only
        }
        return true; // Keep non-bi-monthly format periods
      });
    } else if (assignmentFrequency === 'quarterly') {
      // For quarterly assignments, keep only year start quarter
      return assignmentPeriods.filter(period => {
        const quarterMatch = period.match(/^Q(\d+)-(\d{4})$/);
        if (quarterMatch) {
          const quarter = parseInt(quarterMatch[1]);
          return quarter === 1; // Q1 only
        }
        return true; // Keep non-quarterly format periods
      });
    } else if (assignmentFrequency === 'half-yearly') {
      // For half-yearly assignments, keep only year start half
      return assignmentPeriods.filter(period => {
        const halfMatch = period.match(/^H(\d+)-(\d{4})$/);
        if (halfMatch) {
          const half = parseInt(halfMatch[1]);
          return half === 1; // H1 only
        }
        return true; // Keep non-half-yearly format periods
      });
    }
    return assignmentPeriods; // For other frequencies, return all periods
  }

  /**
   * Check if entity has data for a specific assignment period
   * This handles the case where aggregated data exists but specific assignment periods are missing
   */
  private checkEntityHasDataForAssignmentPeriod(
    dcfId: number,
    level: number,
    entityId: string,
    assignmentPeriod: string,
    existingDataCombinations: Set<string>,
    requestedReportingPeriod: string
  ): boolean {
    // First, try to check for exact assignment period data (monthly granularity)
    const exactKey = `${dcfId}-${level}-${entityId}-${assignmentPeriod}`;
    console.log(exactKey, 'Key 2')
    if (existingDataCombinations.has(exactKey)) {
      return true; // Found exact data for this assignment period
    }

    // For aggregated requests (half-yearly, quarterly), the data might be stored in aggregated format
    // We need to check if the aggregated period exists, but this doesn't guarantee completeness
    // The issue is that aggregated data (H1-2024) might exist even if some months (05-2024, 06-2024) are missing

    // For now, return false if exact data doesn't exist
    // This will force checking at assignment period granularity
    return false;
  }

  /**
   * Convert assignment period to the format used in existing data
   */
  private convertAssignmentPeriodToDataFormat(assignmentPeriod: string, requestedReportingPeriod: string): string {
    if (!requestedReportingPeriod) {
      return assignmentPeriod; // No conversion needed
    }

    const requestedPeriod = requestedReportingPeriod.toLowerCase();

    // Extract year from assignment period (e.g., "01-2024" -> "2024")
    const yearMatch = assignmentPeriod.match(/-(\d{4})$/);
    const year = yearMatch ? yearMatch[1] : new Date().getFullYear().toString();

    // Extract month from assignment period (e.g., "01-2024" -> "01")
    const monthMatch = assignmentPeriod.match(/^(\d{2})-/);
    const monthNum = monthMatch ? parseInt(monthMatch[1]) : 1;

    // Convert based on requested reporting period
    switch (requestedPeriod) {
      case 'half-yearly':
        // Convert monthly assignment period to half-yearly data format
        // H1: Jan(01) - Jun(06), H2: Jul(07) - Dec(12)
        if (monthNum >= 1 && monthNum <= 6) {
          return `H1-${year}`;
        } else {
          return `H2-${year}`;
        }
      case 'quarterly':
        // Convert to quarterly format
        if (monthNum >= 1 && monthNum <= 3) {
          return `Q1-${year}`;
        } else if (monthNum >= 4 && monthNum <= 6) {
          return `Q2-${year}`;
        } else if (monthNum >= 7 && monthNum <= 9) {
          return `Q3-${year}`;
        } else {
          return `Q4-${year}`;
        }
      case 'yearly':
        return year;
      default:
        return assignmentPeriod; // No conversion for monthly or unknown
    }
  }

  /**
   * Construct the proper requested period format based on the reporting period and assignment period
   */
  private constructRequestedPeriod(requestedReportingPeriod: string, assignmentPeriod: string, assignment: any): string {
    try {
      // Extract year from assignment period (e.g., "01-2024" -> "2024")
      const yearMatch = assignmentPeriod.match(/-(\d{4})$/);
      const year = yearMatch ? yearMatch[1] : new Date().getFullYear().toString();

      // Extract month from assignment period (e.g., "01-2024" -> "01")
      const monthMatch = assignmentPeriod.match(/^(\d{2})-/);
      const monthNum = monthMatch ? parseInt(monthMatch[1]) : 1;

      // Map reporting period types to their proper format
      switch (requestedReportingPeriod.toLowerCase()) {
        case 'half-yearly':
          // Determine which half based on the month number
          // H1: Jan(01) - Jun(06), H2: Jul(07) - Dec(12)
          if (monthNum >= 1 && monthNum <= 6) {
            return `H1-${year}`;
          } else {
            return `H2-${year}`;
          }
        case 'quarterly':
          // Determine which quarter based on the month number
          // Q1: Jan(01)-Mar(03), Q2: Apr(04)-Jun(06), Q3: Jul(07)-Sep(09), Q4: Oct(10)-Dec(12)
          if (monthNum >= 1 && monthNum <= 3) {
            return `Q1-${year}`;
          } else if (monthNum >= 4 && monthNum <= 6) {
            return `Q2-${year}`;
          } else if (monthNum >= 7 && monthNum <= 9) {
            return `Q3-${year}`;
          } else {
            return `Q4-${year}`;
          }
        case 'yearly':
          return year;
        case 'monthly':
          return assignmentPeriod; // For monthly, the assignment period is already correct
        default:
          return requestedReportingPeriod; // Fallback to original
      }
    } catch (error) {
      console.error('Error constructing requested period:', error);
      return requestedReportingPeriod; // Fallback to original
    }
  }

  private getQuarterStartMonth(quarter: string): string {
    switch (quarter) {
      case 'Q1': return 'Jan';
      case 'Q2': return 'Apr';
      case 'Q3': return 'Jul';
      case 'Q4': return 'Oct';
      default: return 'Jan';
    }
  }

  private getQuarterEndMonth(quarter: string): string {
    switch (quarter) {
      case 'Q1': return 'Mar';
      case 'Q2': return 'Jun';
      case 'Q3': return 'Sep';
      case 'Q4': return 'Dec';
      default: return 'Mar';
    }
  }

  private getHalfYearStartMonth(half: string): string {
    switch (half) {
      case 'H1': return 'Jan';
      case 'H2': return 'Jul';
      default: return 'Jan';
    }
  }

  private getHalfYearEndMonth(half: string): string {
    switch (half) {
      case 'H1': return 'Jun';
      case 'H2': return 'Dec';
      default: return 'Jun';
    }
  }

  private getBiMonthlyStartMonth(biMonth: string): string {
    switch (biMonth) {
      case 'BM1': return 'Jan';
      case 'BM2': return 'Mar';
      case 'BM3': return 'May';
      case 'BM4': return 'Jul';
      case 'BM5': return 'Sep';
      case 'BM6': return 'Nov';
      default: return 'Jan';
    }
  }

  private getBiMonthlyEndMonth(biMonth: string): string {
    switch (biMonth) {
      case 'BM1': return 'Feb';
      case 'BM2': return 'Apr';
      case 'BM3': return 'Jun';
      case 'BM4': return 'Aug';
      case 'BM5': return 'Oct';
      case 'BM6': return 'Dec';
      default: return 'Feb';
    }
  }

  private getBiMonthlyRange(biMonthNum: number): {start: number; end: number} {
    const ranges = {
      1: {start: 1, end: 2},   // BM1: Jan-Feb
      2: {start: 3, end: 4},   // BM2: Mar-Apr
      3: {start: 5, end: 6},   // BM3: May-Jun
      4: {start: 7, end: 8},   // BM4: Jul-Aug
      5: {start: 9, end: 10},  // BM5: Sep-Oct
      6: {start: 11, end: 12}  // BM6: Nov-Dec
    };
    return ranges[biMonthNum as keyof typeof ranges] || {start: 1, end: 2};
  }

  // Data fetching methods that split data by valid periods
  // Note: valid_periods can contain any subset of possible periods (partial or complete)
  // Examples: ["2025-01"] or ["2025-01", "2025-03", "2025-05"] or ["2025-01", "2025-02", ..., "2025-12"]
  private async fetchMonthlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {


    const periodData = valid_periods.map(period => {
      // Extract year and month from period (e.g., "2025-01")
      const [year, month] = period.split('-');
      const monthNum = parseInt(month);

      // Filter data points that fall within this month using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Check if the period exists in the rp array
        // Keep the same format as the input period (MM-YYYY)
        const targetPeriod = period; // Use the period directly since it's already in the correct format
        const hasMatch = dataPoint.rp.includes(targetPeriod);



        return hasMatch;
      });

      return {
        period,
        period_label: `${this.getMonthName(monthNum)} ${year}`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'monthly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["BM1-2025"] or ["BM1-2025", "BM3-2025"] or ["BM1-2025", "BM2-2025", ..., "BM6-2025"]
  private async fetchBiMonthlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {


    const periodData = valid_periods.map(period => {
      // Extract bi-month and year from period (e.g., "BM1-2025")
      const [biMonth, year] = period.split('-');
      const biMonthNum = parseInt(biMonth.replace('BM', ''));

      // Define bi-monthly month ranges
      const biMonthRanges = {
        1: {start: 1, end: 2},   // BM1: Jan-Feb
        2: {start: 3, end: 4},   // BM2: Mar-Apr
        3: {start: 5, end: 6},   // BM3: May-Jun
        4: {start: 7, end: 8},   // BM4: Jul-Aug
        5: {start: 9, end: 10},  // BM5: Sep-Oct
        6: {start: 11, end: 12}  // BM6: Nov-Dec
      };

      const range = biMonthRanges[biMonthNum as keyof typeof biMonthRanges];

      // Filter data points that fall within this bi-monthly period using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this bi-monthly period
        const biMonthlyMonths: string[] = [];
        for (let month = range.start; month <= range.end; month++) {
          biMonthlyMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if rp contains months from this bi-monthly period
        const rpMonthsInPeriod = dataPoint.rp.filter((rpMonth: string) => biMonthlyMonths.includes(rpMonth));

        // Must have at least one month from this period
        if (rpMonthsInPeriod.length === 0) return false;

        // Check if rp spans multiple bi-monthly periods (invalid)
        const biMonthlyPeriodsSpanned = new Set<number>();
        for (const rpMonth of dataPoint.rp) {
          const [monthStr, yearStr] = rpMonth.split('-');
          if (yearStr !== year) continue; // Different year, ignore

          const monthNum = parseInt(monthStr);
          // Find which bi-monthly period this month belongs to
          if (monthNum >= 1 && monthNum <= 2) biMonthlyPeriodsSpanned.add(1);
          else if (monthNum >= 3 && monthNum <= 4) biMonthlyPeriodsSpanned.add(2);
          else if (monthNum >= 5 && monthNum <= 6) biMonthlyPeriodsSpanned.add(3);
          else if (monthNum >= 7 && monthNum <= 8) biMonthlyPeriodsSpanned.add(4);
          else if (monthNum >= 9 && monthNum <= 10) biMonthlyPeriodsSpanned.add(5);
          else if (monthNum >= 11 && monthNum <= 12) biMonthlyPeriodsSpanned.add(6);
        }

        // Valid if rp doesn't span multiple bi-monthly periods
        return biMonthlyPeriodsSpanned.size <= 1;
      });

      return {
        period,
        period_label: `${biMonth} ${year} (${this.getMonthName(range.start)}-${this.getMonthName(range.end)})`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'bi-monthly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["Q1-2025"] or ["Q1-2025", "Q3-2025"] or ["Q1-2025", "Q2-2025", "Q3-2025", "Q4-2025"]
  private async fetchQuarterlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {


    const periodData = valid_periods.map(period => {
      // Extract quarter and year from period (e.g., "Q1-2025")
      const [quarter, year] = period.split('-');
      const quarterNum = parseInt(quarter.replace('Q', ''));

      // Define quarter month ranges
      const quarterRanges = {
        1: {start: 1, end: 3},   // Q1: Jan-Mar
        2: {start: 4, end: 6},   // Q2: Apr-Jun
        3: {start: 7, end: 9},   // Q3: Jul-Sep
        4: {start: 10, end: 12}  // Q4: Oct-Dec
      };

      const range = quarterRanges[quarterNum as keyof typeof quarterRanges];

      // Filter data points that fall within this quarter using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this quarter
        const quarterMonths: string[] = [];
        for (let month = range.start; month <= range.end; month++) {
          quarterMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if rp contains months from this quarter
        const rpMonthsInPeriod = dataPoint.rp.filter((rpMonth: string) => quarterMonths.includes(rpMonth));

        // Must have at least one month from this period
        if (rpMonthsInPeriod.length === 0) return false;

        // Check if rp spans multiple quarters (invalid)
        const quartersSpanned = new Set<number>();
        for (const rpMonth of dataPoint.rp) {
          const [monthStr, yearStr] = rpMonth.split('-');
          if (yearStr !== year) continue; // Different year, ignore

          const monthNum = parseInt(monthStr);
          // Find which quarter this month belongs to
          if (monthNum >= 1 && monthNum <= 3) quartersSpanned.add(1);
          else if (monthNum >= 4 && monthNum <= 6) quartersSpanned.add(2);
          else if (monthNum >= 7 && monthNum <= 9) quartersSpanned.add(3);
          else if (monthNum >= 10 && monthNum <= 12) quartersSpanned.add(4);
        }

        // Valid if rp doesn't span multiple quarters
        return quartersSpanned.size <= 1;
      });

      return {
        period,
        period_label: `${quarter} ${year} (${this.getMonthName(range.start)}-${this.getMonthName(range.end)})`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'quarterly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["H1-2025"] or ["H2-2025"] or ["H1-2025", "H2-2025"]
  private async fetchHalfYearlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {


    const periodData = valid_periods.map(period => {
      // Extract half and year from period (e.g., "H1-2025")
      const [half, year] = period.split('-');
      const halfNum = parseInt(half.replace('H', ''));

      // Define half-year month ranges
      const halfRanges = {
        1: {start: 1, end: 6},   // H1: Jan-Jun
        2: {start: 7, end: 12}   // H2: Jul-Dec
      };

      const range = halfRanges[halfNum as keyof typeof halfRanges];

      // Filter data points that fall within this half-year using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this half-year
        const halfYearMonths: string[] = [];
        for (let month = range.start; month <= range.end; month++) {
          halfYearMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if rp contains months from this half-year
        const rpMonthsInPeriod = dataPoint.rp.filter((rpMonth: string) => halfYearMonths.includes(rpMonth));

        // Must have at least one month from this period
        if (rpMonthsInPeriod.length === 0) return false;

        // Check if rp spans multiple half-years (invalid)
        const halfYearsSpanned = new Set<number>();
        for (const rpMonth of dataPoint.rp) {
          const [monthStr, yearStr] = rpMonth.split('-');
          if (yearStr !== year) continue; // Different year, ignore

          const monthNum = parseInt(monthStr);
          // Find which half-year this month belongs to
          if (monthNum >= 1 && monthNum <= 6) halfYearsSpanned.add(1);
          else if (monthNum >= 7 && monthNum <= 12) halfYearsSpanned.add(2);
        }

        // Valid if rp doesn't span multiple half-years
        return halfYearsSpanned.size <= 1;
      });

      return {
        period,
        period_label: `${half} ${year} (${this.getMonthName(range.start)}-${this.getMonthName(range.end)})`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'half-yearly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["2025"] or ["2024", "2025"] or ["2023", "2024", "2025"]
  private async fetchYearlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {


    const periodData = valid_periods.map(period => {
      // Period is just the year (e.g., "2025")
      const year = parseInt(period);

      // Filter data points that fall within this year using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this year and check if any exist in rp array
        const yearMonths = [];
        for (let month = 1; month <= 12; month++) {
          yearMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if any year month exists in the rp array
        return yearMonths.some(monthPeriod => dataPoint.rp.includes(monthPeriod));
      });

      return {
        period,
        period_label: `Year ${year}`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'yearly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  private resolveReportingFrequency(
    input: ReportingFrequencyInput,
    assignment: any
  ): {isValid: boolean; periods?: ReportingPeriod; error?: string} {
    try {
      // First validate frequency compatibility
      const frequencyValidation = this.validateFrequencyCompatibility(
        input.reporting_period,
        assignment,
        input.reporting_period_from,
        input.reporting_period_to,
        input.year
      );

      if (!frequencyValidation.isValid) {
        return {
          isValid: false,
          error: frequencyValidation.incompatibleReason
        };
      }

      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      let periods: ReportingPeriod;

      // Handle custom period
      if (input.reporting_period.toLowerCase() === 'custom') {
        if (!input.reporting_period_from || !input.reporting_period_to) {
          return {
            isValid: false,
            error: 'Custom period requires both start and end dates'
          };
        }

        const startDate = new Date(input.reporting_period_from);
        const endDate = new Date(input.reporting_period_to);
        const validPeriods: string[] = [];

        let currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          const year = currentDate.getFullYear();
          const month = currentDate.getMonth();
          validPeriods.push(`${year}-${(month + 1).toString().padStart(2, '0')}`);
          currentDate.setMonth(currentDate.getMonth() + 1);
        }

        // Calculate start and end months for custom period
        const startMonth = `${months[startDate.getMonth()]}-${startDate.getFullYear()}`;
        const endMonth = `${months[endDate.getMonth()]}-${endDate.getFullYear()}`;

        periods = {
          valid_periods: validPeriods,
          data_granularity: input.reporting_frequency,
          start_month: startMonth,
          end_month: endMonth
        };
      } else {
        // Handle standard periods
        const yearValue = Array.isArray(input.year) ? input.year[0] : input.year;

        // Use assignment frequency to generate periods, not the requested reporting period
        // This ensures that assignments with more granular frequencies generate appropriate periods
        const assignmentFrequency = input.reporting_frequency.toLowerCase();

        switch (assignmentFrequency) {
          case 'monthly':
            periods = {
              valid_periods: months.map((_month, index) =>
                `${(index + 1).toString().padStart(2, '0')}-${yearValue}`
              ),
              data_granularity: 'monthly',
              start_month: `Jan-${yearValue}`,
              end_month: `Dec-${yearValue}`
            };
            break;

          case 'bi-monthly':
            periods = {
              valid_periods: Array.from({length: 6}, (_, i) =>
                `BM${i + 1}-${yearValue}`
              ),
              data_granularity: 'bi-monthly',
              start_month: `Jan-${yearValue}`,
              end_month: `Dec-${yearValue}`
            };
            break;

          case 'quarterly':
            periods = {
              valid_periods: ['Q1', 'Q2', 'Q3', 'Q4'].map(q => `${q}-${yearValue}`),
              data_granularity: 'quarterly',
              start_month: `Jan-${yearValue}`,
              end_month: `Dec-${yearValue}`
            };
            break;

          case 'half-yearly':
            periods = {
              valid_periods: ['H1', 'H2'].map(h => `${h}-${yearValue}`),
              data_granularity: 'half-yearly',
              start_month: `Jan-${yearValue}`,
              end_month: `Dec-${yearValue}`
            };
            break;

          case 'yearly':
            periods = {
              valid_periods: [yearValue.toString()],
              data_granularity: 'yearly',
              start_month: `Jan-${yearValue}`,
              end_month: `Dec-${yearValue}`
            };
            break;

          default:
            return {
              isValid: false,
              error: `Unsupported assignment frequency: ${input.reporting_frequency}`
            };
        }
      }

      return {
        isValid: true,
        periods
      };

    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error in resolving reporting frequency'
      };
    }
  }

  formatDateToMonthYear(date: any) {
    // Get month short name (e.g., Jan, Feb, etc.)
    const monthShort = date.toLocaleString("en-US", {month: "short"});
    // Get year
    const year = date.getFullYear();
    // Combine
    return `${monthShort}-${year}`;
  }

  /**
   * Process data based on type_of_format
   * @param type_of_format - Format type: 'value_field', 'tabular_form_data', 'chart_data'
   * @param table_config - Table configuration object
   * @param chart_config - Chart configuration object
   * @param data_sets - Calculated data sets array
   * @param valid_periods - Valid periods array
   * @param reporting_period - Reporting period type
   * @param period_data - Period data arrays
   * @param dcfIds - DCF IDs array
   * @param entity - Entity array
   * @param sub_data_source - Sub data source type
   * @param raw_parameters - Raw parameters
   * @param indicator_parameters - Indicator parameters
   */
  private async type_of_format(
    type_of_format: string,
    table_config: any,
    chart_config: any,
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    entity: string[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any,
    type_of_data?: string, // New parameter to distinguish between direct_extract and queried_data
    year?: number[], // New parameter for year array (used for queried_data)
    activeFilterType?: any // New parameter for accessing filter data like reporting_period
  ): Promise<any> {
    try {
      switch (type_of_format) {
        case 'value_field':
          // Return single reduced value from all data_sets
          const totalValue = data_sets.reduce((sum, value) => sum + value, 0);
          return {
            format_type: 'value_field',
            value: totalValue
          };

        case 'tabular_form_data':

          return await this.processTabularFormat(
            table_config,
            data_sets,
            valid_periods,
            reporting_period,
            period_data,
            dcfIds,
            entity,
            sub_data_source,
            raw_parameters,
            indicator_parameters,
            type_of_data,
            year,
            activeFilterType
          );

        case 'chart_data':
          return await this.processChartFormat(
            chart_config,
            data_sets,
            valid_periods,
            reporting_period,
            period_data,
            dcfIds,
            entity,
            sub_data_source,
            raw_parameters,
            indicator_parameters,
            type_of_data,
            year,
            activeFilterType
          );

        default:
          return {
            format_type: 'unknown',
            error: `Unsupported format type: ${type_of_format}`
          };
      }
    } catch (error) {
      return {
        format_type: 'error',
        error: `Error processing format: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process tabular format data
   */
  private async processTabularFormat(
    table_config: any,
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    entity: string[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any,
    type_of_data?: string,
    year?: number[],
    activeFilterType?: any
  ): Promise<any> {
    try {
      const result: any = {
        format_type: 'tabular_form_data'
      };

      // Only include breakdowns that are explicitly set to true
      // Note: If no breakdown is enabled, period_breakdown will be used as default
      const hasPeriodBreakdown = table_config?.period_breakdown === true;
      const hasEntityBreakdown = table_config?.entity_breakdown === true;
      const hasDcfBreakdown = table_config?.dcf_breakdown === true;
      const hasDpBreakdown = table_config?.dp_breakdown === true;

      // If no breakdown is enabled, default to period_breakdown to show data
      const hasAnyBreakdown = hasPeriodBreakdown || hasEntityBreakdown || hasDcfBreakdown || hasDpBreakdown;
      const shouldShowPeriodBreakdown = hasPeriodBreakdown || !hasAnyBreakdown;

      // Add period_breakdown if explicitly enabled or if no breakdown is enabled (default)
      if (shouldShowPeriodBreakdown) {
        if (type_of_data === 'queried_data') {
          result.period_breakdown = await this.generateNewPeriodBreakdown(
            data_sets,
            valid_periods,
            year,
            period_data,
            entity,
            activeFilterType?.reporting_period || reporting_period
          );
        } else {
          result.period_breakdown = this.generatePeriodBreakdown(data_sets, valid_periods, reporting_period);
        }
      }

      // Add entity_breakdown only if explicitly enabled
      if (hasEntityBreakdown) {
        if (type_of_data === 'queried_data') {
          // For queried_data, use year-based entity breakdown
          result.entity_breakdown = await this.generateYearBasedEntityBreakdown(
            data_sets,
            valid_periods,
            year,
            period_data,
            entity,
            activeFilterType?.reporting_period || reporting_period
          );
        } else {
          // For direct_extract, use existing entity breakdown
          result.entity_breakdown = await this.generateEntityBreakdown(
            data_sets,
            valid_periods,
            reporting_period,
            period_data,
            entity
          );
        }
      }

      // Add dcf_breakdown only if explicitly enabled
      if (hasDcfBreakdown) {
        // For queried_data, use new period-based DCF breakdown
        if (type_of_data === 'queried_data') {
          result.dcf_breakdown = await this.generateNewDcfBreakdown(
            data_sets,
            valid_periods,
            year,
            period_data,
            entity,
            dcfIds,
            sub_data_source,
            raw_parameters,
            indicator_parameters,
            activeFilterType?.reporting_period || reporting_period
          );
        } else {
          // For direct_extract, keep existing behavior
          result.dcf_breakdown = await this.generateDcfBreakdown(
            data_sets,
            valid_periods,
            reporting_period,
            period_data,
            dcfIds,
            sub_data_source,
            raw_parameters,
            indicator_parameters
          );
        }
      }

      // Add dp_breakdown only if explicitly enabled
      if (hasDpBreakdown) {
        // For queried_data, use new period-based DP breakdown
        if (type_of_data === 'queried_data') {
          result.dp_breakdown = await this.generateNewDpBreakdown(
            data_sets,
            valid_periods,
            year,
            period_data,
            entity,
            dcfIds,
            sub_data_source,
            raw_parameters,
            indicator_parameters,
            activeFilterType?.reporting_period || reporting_period
          );
        } else {
          // For direct_extract, keep existing behavior
          result.dp_breakdown = await this.generateDpBreakdown(
            data_sets,
            valid_periods,
            reporting_period,
            period_data,
            dcfIds,
            sub_data_source,
            raw_parameters,
            indicator_parameters
          );
        }
      }

      return result;

    } catch (error) {
      return {
        format_type: 'tabular_error',
        error: `Error processing tabular format: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process chart format data - returns standard chart structure with chart_type, x_axis, and series
   */
  private async processChartFormat(
    chart_config: any,
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    entity: string[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any,
    type_of_data?: string,
    year?: number[],
    activeFilterType?: any
  ): Promise<any> {
    try {
      // Only include breakdowns that are explicitly set to true
      // Note: If no breakdown is enabled, period_breakdown will be used as default
      const hasPeriodBreakdown = chart_config?.period_breakdown === true;
      const hasEntityBreakdown = chart_config?.entity_breakdown === true;
      const hasDcfBreakdown = chart_config?.dcf_breakdown === true;
      const hasDpBreakdown = chart_config?.dp_breakdown === true;

      // If no breakdown is enabled, default to period_breakdown to show data
      const hasAnyBreakdown = hasPeriodBreakdown || hasEntityBreakdown || hasDcfBreakdown || hasDpBreakdown;
      const shouldShowPeriodBreakdown = hasPeriodBreakdown || !hasAnyBreakdown;

      // Get chart type from config, default to 'bar'
      const chart_type = chart_config?.chart_type || 'bar';

      // Determine x_axis labels - should match tabular format columns
      let x_axis: string[] = [];
      if (hasEntityBreakdown || (shouldShowPeriodBreakdown && type_of_data === 'queried_data') ||
        (hasDcfBreakdown && type_of_data === 'queried_data') ||
        (hasDpBreakdown && type_of_data === 'queried_data')) {
        // For entity, period, DCF, or DP breakdown in queried_data, use period-based columns with year context
        x_axis = this.generatePeriodColumns(activeFilterType?.reporting_period || reporting_period, year, true);
      } else if (type_of_data === 'queried_data') {
        // For other queried_data breakdowns, use year-based labels
        x_axis = year ? year.map(y => y.toString()) : ['Year'];
      } else {
        // For direct_extract, use same logic as tabular format columns
        x_axis = valid_periods.map(period => this.formatPeriodForDisplay(period));
      }

      // Initialize series array
      const series: any[] = [];

      // Generate series based on enabled breakdowns
      if (shouldShowPeriodBreakdown) {
        if (type_of_data === 'queried_data') {
          // For queried_data, use new period breakdown structure
          const periodBreakdown = await this.generateNewPeriodBreakdown(
            data_sets,
            valid_periods,
            year,
            period_data,
            entity,
            activeFilterType?.reporting_period || reporting_period
          );

          // Convert period breakdown to chart series - single series aggregated across years
          if (periodBreakdown && periodBreakdown.rows && periodBreakdown.rows.length > 0) {
            const row = periodBreakdown.rows[0]; // Single row with aggregated data
            series.push({
              label: row.label || 'Total',
              data: row.values || []
            });
          }
        } else {
          // For direct_extract, use existing logic
          series.push({
            label: this.getSeriesLabel('period', reporting_period, type_of_data),
            data: data_sets
          });
        }
      }

      if (hasEntityBreakdown) {
        // Entity breakdown: multiple series, one for each entity
        const entitySeries = await this.generateEntityChartSeries(
          data_sets,
          valid_periods,
          period_data,
          entity,
          type_of_data,
          year,
          reporting_period,
          activeFilterType
        );
        series.push(...entitySeries);
      }

      if (hasDcfBreakdown) {
        // DCF breakdown: multiple series, one for each DCF
        const dcfSeries = await this.generateDcfChartSeries(
          data_sets,
          valid_periods,
          period_data,
          dcfIds,
          sub_data_source,
          raw_parameters,
          indicator_parameters,
          type_of_data,
          year,
          reporting_period,
          entity,
          activeFilterType
        );
        series.push(...dcfSeries);
      }

      if (hasDpBreakdown) {
        // DP breakdown: multiple series, one for each data point
        const dpSeries = await this.generateDpChartSeries(
          data_sets,
          valid_periods,
          period_data,
          dcfIds,
          sub_data_source,
          raw_parameters,
          indicator_parameters,
          type_of_data,
          year,
          reporting_period,
          entity,
          activeFilterType
        );
        series.push(...dpSeries);
      }

      return {
        format_type: 'chart_data',
        chart_type,
        x_axis,
        series
      };

    } catch (error) {
      return {
        format_type: 'chart_error',
        error: `Error processing chart format: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }



  /**
   * Get series label based on breakdown type
   */
  private getSeriesLabel(breakdownType: string, reporting_period: string, type_of_data?: string): string {
    switch (breakdownType) {
      case 'period':
        if (type_of_data === 'queried_data') {
          return 'Total by Year';
        }
        return `Total by ${reporting_period}`;
      case 'entity':
        return 'Entity Data';
      case 'dcf':
        return 'DCF Data';
      default:
        return 'Data';
    }
  }

  /**
   * Generate entity chart series - one series per entity
   */
  private async generateEntityChartSeries(
    data_sets: number[],
    valid_periods: string[],
    period_data: any[][],
    entities: string[],
    type_of_data?: string,
    year?: number[],
    reporting_period?: string,
    activeFilterType?: any
  ): Promise<any[]> {
    const series: any[] = [];

    // Get entity breakdown data
    let entityBreakdown: any;
    if (type_of_data === 'queried_data') {
      entityBreakdown = await this.generateYearBasedEntityBreakdown(
        data_sets,
        valid_periods,
        year,
        period_data,
        entities,
        activeFilterType?.reporting_period || reporting_period
      );
    } else {
      entityBreakdown = await this.generateEntityBreakdown(
        data_sets,
        valid_periods,
        reporting_period || 'monthly',
        period_data,
        entities
      );
    }

    // Convert entity breakdown to chart series
    if (entityBreakdown && entityBreakdown.rows) {
      entityBreakdown.rows.forEach((row: any) => {
        series.push({
          label: row.entity || 'Unknown Entity',
          data: [row.value || 0]
        });
      });
    }

    return series;
  }

  /**
   * Generate DCF chart series - one series per DCF
   */
  private async generateDcfChartSeries(
    data_sets: number[],
    valid_periods: string[],
    period_data: any[][],
    dcfIds: number[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any,
    type_of_data?: string,
    year?: number[],
    reporting_period?: string,
    entity?: string[],
    activeFilterType?: any
  ): Promise<any[]> {
    const series: any[] = [];

    // Get DCF breakdown data
    let dcfBreakdown: any;
    if (type_of_data === 'queried_data') {
      dcfBreakdown = await this.generateNewDcfBreakdown(
        data_sets,
        valid_periods,
        year,
        period_data,
        entity,
        dcfIds,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        activeFilterType?.reporting_period || reporting_period
      );
    } else {
      dcfBreakdown = await this.generateDcfBreakdown(
        data_sets,
        valid_periods,
        reporting_period || 'monthly',
        period_data,
        dcfIds,
        sub_data_source,
        raw_parameters,
        indicator_parameters
      );
    }

    // Convert DCF breakdown to chart series
    if (dcfBreakdown && dcfBreakdown.rows) {
      dcfBreakdown.rows.forEach((row: any) => {
        series.push({
          label: row.dcf_name || 'Unknown DCF',
          data: [row.value || 0]
        });
      });
    }

    return series;
  }

  /**
   * Generate DP chart series - one series per data point
   */
  private async generateDpChartSeries(
    data_sets: number[],
    valid_periods: string[],
    period_data: any[][],
    dcfIds: number[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any,
    type_of_data?: string,
    year?: number[],
    reporting_period?: string,
    entity?: string[],
    activeFilterType?: any
  ): Promise<any[]> {
    const series: any[] = [];

    // Get DP breakdown data
    let dpBreakdown: any;
    if (type_of_data === 'queried_data') {
      dpBreakdown = await this.generateNewDpBreakdown(
        data_sets,
        valid_periods,
        year,
        period_data,
        entity,
        dcfIds,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        activeFilterType?.reporting_period || reporting_period
      );
    } else {
      dpBreakdown = await this.generateDpBreakdown(
        data_sets,
        valid_periods,
        reporting_period || 'monthly',
        period_data,
        dcfIds,
        sub_data_source,
        raw_parameters,
        indicator_parameters
      );
    }

    // Convert DP breakdown to chart series
    if (dpBreakdown && dpBreakdown.rows) {
      dpBreakdown.rows.forEach((row: any) => {
        series.push({
          label: row.data_point || 'Unknown Data Point',
          data: [row.value || 0]
        });
      });
    }

    return series;
  }

  /**
   * Generate period breakdown table
   */
  private generatePeriodBreakdown(
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string
  ): any {
    // Convert periods to column headers
    const columns = valid_periods.map(period => this.formatPeriodForDisplay(period));

    return {
      table_type: 'period_breakdown',
      reporting_period: reporting_period.toLowerCase(),
      columns,
      rows: [
        {
          label: 'Total Consumption',
          values: data_sets
        }
      ]
    };
  }

  /**
   * Check if a period belongs to a specific year
   */
  private periodBelongsToYear(period: string, year: number): boolean {
    // Handle different period formats
    if (period.includes('-')) {
      // Formats like "Q1-2024", "H1-2024", "BM1-2024", "01-2024"
      const yearPart = period.split('-')[1];
      return parseInt(yearPart) === year;
    } else if (period.match(/^\d{4}$/)) {
      // Yearly format: "2024"
      return parseInt(period) === year;
    }
    return false;
  }

  /**
   * Generate year-based period breakdown table for queried_data
   */
  private generateYearBasedPeriodBreakdown(
    data_sets: number[],
    valid_periods: string[],
    year?: number[]
  ): any {
    // For queried_data, use year-based column headers and aggregate values by year
    const columns = year ? year.map(y => y.toString()) : ['Year'];

    let yearBasedValues: number[] = [];

    if (year && year.length > 0) {
      // Aggregate data_sets values by year
      // If we have multiple periods per year, sum them up
      yearBasedValues = year.map(yearValue => {
        // Find all periods that belong to this year and sum their values
        let yearTotal = 0;
        valid_periods.forEach((period, index) => {
          if (this.periodBelongsToYear(period, yearValue)) {
            yearTotal += data_sets[index] || 0;
          }
        });
        return yearTotal;
      });
    } else {
      // Fallback to original data_sets if no year provided
      yearBasedValues = data_sets;
    }

    return {
      table_type: 'period_breakdown',
      reporting_period: 'year-based',
      columns,
      rows: [
        {
          label: 'Total Consumption',
          values: yearBasedValues
        }
      ]
    };
  }

  /**
   * Generate entity breakdown table
   */
  private async generateEntityBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    entity: string[]
  ): Promise<any> {
    try {
      // Get entity details using the provided query
      const filteredLocations = await this.userProfileRepository.locationOnes(94).find({
        include: [
          {
            relation: "locationTwos",
            scope: {
              include: [{relation: "locationThrees"}],
            },
          },
        ],
      });

      const shapedSite = filteredLocations.map(item => {
        if (item.locationTwos) {
          item.locationTwos = item.locationTwos.filter(locationTwo =>
            locationTwo.locationThrees && locationTwo.locationThrees.length > 0
          );
        }
        return item;
      }).filter(item => item.locationTwos && item.locationTwos.length > 0);

      // Create entity name mapping
      const entityNameMap = new Map<string, string>();
      shapedSite.forEach(location => {
        const level1Key = `1-${location.id}`;
        entityNameMap.set(level1Key, location.name || '');

        location.locationTwos?.forEach((locationTwo: any) => {
          const level2Key = `2-${locationTwo.id}`;
          entityNameMap.set(level2Key, locationTwo.name || '');

          locationTwo.locationThrees?.forEach((locationThree: any) => {
            const level3Key = `3-${locationThree.id}`;
            entityNameMap.set(level3Key, locationThree.name || '');
          });
        });
      });

      // Generate rows for each entity
      const rows = entity.map(entityKey => {
        const entityName = entityNameMap.get(entityKey) || entityKey;

        // Calculate total value across all periods for this entity
        let totalValue = 0;
        valid_periods.forEach((_period, periodIndex) => {
          const periodDataArray = period_data[periodIndex] || [];

          // Filter data for this specific entity
          const entityData = periodDataArray.filter((dataPoint: any) => {
            const dataEntityKey = `${dataPoint.level}-${dataPoint.locationId}`;
            return dataEntityKey === entityKey;
          });

          // Sum computedValue for this entity in this period
          totalValue += entityData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
        });

        return {
          entity: entityName,
          value: parseFloat(totalValue.toFixed(4))
        };
      });

      return {
        table_type: 'entity_breakdown',
        reporting_period: reporting_period.toLowerCase(),
        rows
      };

    } catch (error) {
      console.error('Error generating entity breakdown:', error);
      return {
        error: `Error generating entity breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate period columns based on reporting_period and year context
   */
  private generatePeriodColumns(reporting_period: string, year?: number[], showYear: boolean = false): string[] {
    const basePeriods = this.getBasePeriods(reporting_period);

    if (showYear && year && year.length === 1) {
      // Single year: show period with year (Q1-2024, H1-2024, etc.)
      return basePeriods.map(period => `${period}-${year[0]}`);
    } else {
      // Multiple years or no year context: show period only (Q1, H1, etc.)
      return basePeriods;
    }
  }

  /**
   * Get base period names without year
   */
  private getBasePeriods(reporting_period: string): string[] {
    switch (reporting_period.toLowerCase()) {
      case 'quarterly':
        return ['Q1', 'Q2', 'Q3', 'Q4'];
      case 'monthly':
        return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      case 'bi-monthly':
        return ['BM1', 'BM2', 'BM3', 'BM4', 'BM5', 'BM6'];
      case 'half-yearly':
      case 'half yearly':
        return ['H1', 'H2'];
      case 'yearly':
      case 'annual':
        return ['Annual'];
      default:
        // Default to quarterly if unknown
        return ['Q1', 'Q2', 'Q3', 'Q4'];
    }
  }

  /**
   * Generate year range display (e.g., "2023-2024" for [2023, 2024])
   */
  private generateYearRangeDisplay(year?: number[]): string {
    if (!year || year.length === 0) return '';
    if (year.length === 1) return year[0].toString();

    const sortedYears = [...year].sort((a, b) => a - b);
    return `${sortedYears[0]}-${sortedYears[sortedYears.length - 1]}`;
  }

  /**
   * Check if a period matches a specific column based on reporting_period
   */
  private periodMatchesColumn(period: string, column: string, reporting_period: string): boolean {
    const periodLower = period.toLowerCase();
    const columnLower = column.toLowerCase();

    // Extract base column name (remove year if present)
    // e.g., "q1-2024" -> "q1", "h1-2024" -> "h1", "jan-2024" -> "jan"
    const baseColumn = columnLower.split('-')[0];

    // Extract base period name (remove year if present)
    // e.g., "Q1-2024" -> "q1", "Jan-2024" -> "jan"
    const basePeriod = periodLower.split('-')[0];

    switch (reporting_period.toLowerCase()) {
      case 'quarterly':
        // Handle periods in format like 'Q1-2024', 'Q2-2024', etc.
        if (baseColumn === 'q1') return basePeriod === 'q1' || periodLower.includes('jan') || periodLower.includes('feb') || periodLower.includes('mar');
        if (baseColumn === 'q2') return basePeriod === 'q2' || periodLower.includes('apr') || periodLower.includes('may') || periodLower.includes('jun');
        if (baseColumn === 'q3') return basePeriod === 'q3' || periodLower.includes('jul') || periodLower.includes('aug') || periodLower.includes('sep');
        if (baseColumn === 'q4') return basePeriod === 'q4' || periodLower.includes('oct') || periodLower.includes('nov') || periodLower.includes('dec');
        break;
      case 'monthly':
        // For monthly, handle both numeric (01, 02, 03) and name (jan, feb, mar) formats
        // Create mapping from numeric month to month name
        const monthMapping: {[key: string]: string} = {
          '01': 'jan', '02': 'feb', '03': 'mar', '04': 'apr',
          '05': 'may', '06': 'jun', '07': 'jul', '08': 'aug',
          '09': 'sep', '10': 'oct', '11': 'nov', '12': 'dec'
        };

        // Check direct match or numeric-to-name mapping
        const monthlyMatch = basePeriod === baseColumn ||
          monthMapping[basePeriod] === baseColumn ||
          periodLower.includes(baseColumn);
        return monthlyMatch;
      case 'bi-monthly':
        // For bi-monthly, check direct match first, then fallback to month names
        if (baseColumn === 'bm1') return basePeriod === 'bm1' || periodLower.includes('jan') || periodLower.includes('feb');
        if (baseColumn === 'bm2') return basePeriod === 'bm2' || periodLower.includes('mar') || periodLower.includes('apr');
        if (baseColumn === 'bm3') return basePeriod === 'bm3' || periodLower.includes('may') || periodLower.includes('jun');
        if (baseColumn === 'bm4') return basePeriod === 'bm4' || periodLower.includes('jul') || periodLower.includes('aug');
        if (baseColumn === 'bm5') return basePeriod === 'bm5' || periodLower.includes('sep') || periodLower.includes('oct');
        if (baseColumn === 'bm6') return basePeriod === 'bm6' || periodLower.includes('nov') || periodLower.includes('dec');
        break;
      case 'half-yearly':
      case 'half yearly':
        if (baseColumn === 'h1') return basePeriod === 'h1' || periodLower.includes('jan') || periodLower.includes('feb') || periodLower.includes('mar') ||
          periodLower.includes('apr') || periodLower.includes('may') || periodLower.includes('jun');
        if (baseColumn === 'h2') return basePeriod === 'h2' || periodLower.includes('jul') || periodLower.includes('aug') || periodLower.includes('sep') ||
          periodLower.includes('oct') || periodLower.includes('nov') || periodLower.includes('dec');
        break;
      case 'yearly':
      case 'annual':
        return true; // All periods belong to annual
      default:
        // Default to quarterly matching
        if (baseColumn === 'q1') return basePeriod === 'q1' || periodLower.includes('jan') || periodLower.includes('feb') || periodLower.includes('mar');
        if (baseColumn === 'q2') return basePeriod === 'q2' || periodLower.includes('apr') || periodLower.includes('may') || periodLower.includes('jun');
        if (baseColumn === 'q3') return basePeriod === 'q3' || periodLower.includes('jul') || periodLower.includes('aug') || periodLower.includes('sep');
        if (baseColumn === 'q4') return basePeriod === 'q4' || periodLower.includes('oct') || periodLower.includes('nov') || periodLower.includes('dec');
    }
    return false;
  }

  /**
   * Generate new period breakdown table for queried_data
   * Columns based on reporting_period, rows for each period, aggregates across multiple dimension (year or entity)
   */
  private async generateNewPeriodBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    year?: number[],
    period_data?: any[][],
    entity?: string[],
    reporting_period?: string
  ): Promise<any> {
    try {
      // Generate columns based on reporting_period without year context (aggregate across years)
      const columns = this.generatePeriodColumns(reporting_period || 'Quarterly', undefined, false);

      // Generate single row aggregating across all years
      const values = columns.map((column) => {
        let columnTotal = 0;

        // Find all periods that match this column across all years
        valid_periods.forEach((periodKey, periodIndex) => {
          if (this.periodMatchesColumn(periodKey, column, reporting_period || 'Quarterly')) {
            if (period_data?.[periodIndex]) {
              const periodDataArray = this.extractPeriodDataArray(period_data[periodIndex]);

              // Sum across all entities for this period/column (across all years)
              columnTotal += this.sumPeriodData(periodDataArray, entity);
            }
          }
        });

        return parseFloat(columnTotal.toFixed(4));
      });

      const rows = [
        {
          label: 'Total',
          values
        }
      ];

      return {
        table_type: 'period_breakdown',
        reporting_period: reporting_period || 'Quarterly',
        columns,
        rows
      };

    } catch (error) {
      console.error('Error generating new period breakdown:', error);
      return {
        error: `Error generating new period breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Helper method to extract period data array from different structures
   */
  private extractPeriodDataArray(periodItem: any): any[] {
    if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
      return periodItem.data;
    } else if (Array.isArray(periodItem)) {
      return periodItem;
    } else if (periodItem) {
      return [periodItem];
    }
    return [];
  }

  /**
   * Helper method to sum period data across entities
   */
  private sumPeriodData(periodDataArray: any[], entities?: string[]): number {
    return periodDataArray.reduce((sum, item) => {
      // If entities are specified, filter by entity
      if (entities && entities.length > 0) {
        const dataEntityKey = `${item.level}-${item.locationId}`;
        const itemEntityName = this.extractEntityName(item);

        const matchesEntity = entities.some(entityKey =>
          dataEntityKey === entityKey ||
          itemEntityName === entityKey ||
          this.matchesEntityFormat(item, entityKey)
        );

        if (!matchesEntity) {
          return sum;
        }
      }

      // Handle different data types for computedValue
      let value = 0;
      if (item.computedValue !== null && item.computedValue !== undefined) {
        if (typeof item.computedValue === 'number') {
          value = item.computedValue;
        } else if (typeof item.computedValue === 'string') {
          // Handle common placeholder values that should be treated as 0
          const trimmedValue = item.computedValue.trim();
          if (trimmedValue === '' || trimmedValue === '-' || trimmedValue === 'N/A' || trimmedValue === 'NA') {
            value = 0;
          } else {
            const parsed = parseFloat(trimmedValue);
            value = isNaN(parsed) ? 0 : parsed;
          }
        }
      }

      return sum + value;
    }, 0);
  }

  /**
   * Generate new DCF breakdown table for queried_data
   * Columns based on reporting_period, rows for each DCF, aggregates across multiple dimension (year or entity)
   */
  private async generateNewDcfBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    year?: number[],
    period_data?: any[][],
    entity?: string[],
    dcfIds?: number[],
    sub_data_source?: string,
    _raw_parameters?: any,
    indicator_parameters?: any,
    reporting_period?: string
  ): Promise<any> {
    try {
      if (!dcfIds || dcfIds.length === 0) {
        return {
          table_type: 'dcf_breakdown',
          reporting_period: reporting_period || 'Quarterly',
          rows: []
        };
      }

      // Get DCF names from formCollectionRepository
      const dcfForms = await this.formCollectionRepository.find({
        where: {
          id: {inq: dcfIds}
        },
        fields: ['id', 'title']
      });

      const dcfNameMap = new Map<number, string>();
      dcfForms.forEach(form => {
        if (form.id !== undefined) {
          dcfNameMap.set(form.id, form.title || '');
        }
      });

      // Generate rows - one for each DCF with total aggregation across all periods
      const rows = dcfIds.map(dcfId => {
        const dcfName = dcfNameMap.get(dcfId) || `DCF ${dcfId}`;

        // Calculate total value across all periods for this DCF
        let totalValue = 0;

        valid_periods.forEach((periodKey, periodIndex) => {
          if (period_data?.[periodIndex]) {
            const periodDataArray = this.extractPeriodDataArray(period_data[periodIndex]);

            // Filter for this DCF and sum across entities
            const dcfData = periodDataArray.filter((dataPoint: any) => dataPoint.dcfId === dcfId);
            totalValue += this.sumPeriodData(dcfData, entity);
          }
        });

        return {
          dcf_name: dcfName,
          value: parseFloat(totalValue.toFixed(4))
        };
      });

      return {
        table_type: 'dcf_breakdown',
        reporting_period: reporting_period || 'Quarterly',
        rows
      };

    } catch (error) {
      console.error('Error generating new DCF breakdown:', error);
      return {
        error: `Error generating new DCF breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate new DP breakdown table for queried_data
   * Columns based on reporting_period, rows for each data point, aggregates across multiple dimension (year or entity)
   */
  private async generateNewDpBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    year?: number[],
    period_data?: any[][],
    entity?: string[],
    dcfIds?: number[],
    sub_data_source?: string,
    _raw_parameters?: any,
    indicator_parameters?: any,
    reporting_period?: string
  ): Promise<any> {
    try {
      // Generate columns based on reporting_period and year context
      const columns = this.generatePeriodColumns(reporting_period || 'Quarterly', year, true);

      // Collect all unique data points across all periods
      const dataPointMap = new Map<string, number[]>();

      // Initialize data point map by scanning all period data
      valid_periods.forEach((periodKey, periodIndex) => {
        if (period_data?.[periodIndex]) {
          const periodDataArray = this.extractPeriodDataArray(period_data[periodIndex]);

          periodDataArray.forEach((dataPoint: any) => {
            const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

            if (!dataPointMap.has(dataPointName)) {
              dataPointMap.set(dataPointName, new Array(columns.length).fill(0));
            }
          });
        }
      });

      // Calculate values for each data point and column
      dataPointMap.forEach((values, dataPointName) => {
        columns.forEach((column, columnIndex) => {
          let columnTotal = 0;

          // Determine which dimension has multiple values (year or entity)
          const hasMultipleYears = year && year.length > 1;
          const hasMultipleEntities = entity && entity.length > 1;

          if (hasMultipleYears) {
            // Aggregate across all years for this data point and column
            year!.forEach(targetYear => {
              valid_periods.forEach((periodKey, periodIndex) => {
                if (this.periodBelongsToYear(periodKey, targetYear) &&
                  this.periodMatchesColumn(periodKey, column, reporting_period || 'Quarterly')) {

                  if (period_data?.[periodIndex]) {
                    const periodDataArray = this.extractPeriodDataArray(period_data[periodIndex]);

                    // Filter for this data point and sum across entities
                    const dpData = periodDataArray.filter((dataPoint: any) => {
                      const dpName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';
                      return dpName === dataPointName;
                    });
                    columnTotal += this.sumPeriodData(dpData, entity);
                  }
                }
              });
            });
          } else if (hasMultipleEntities) {
            // Aggregate across all entities for this data point and column
            valid_periods.forEach((periodKey, periodIndex) => {
              if (this.periodMatchesColumn(periodKey, column, reporting_period || 'Quarterly')) {

                if (period_data?.[periodIndex]) {
                  const periodDataArray = this.extractPeriodDataArray(period_data[periodIndex]);

                  // Filter for this data point and sum across entities
                  const dpData = periodDataArray.filter((dataPoint: any) => {
                    const dpName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';
                    return dpName === dataPointName;
                  });
                  columnTotal += this.sumPeriodData(dpData, entity);
                }
              }
            });
          } else {
            // Single year and single entity
            valid_periods.forEach((periodKey, periodIndex) => {
              if (this.periodMatchesColumn(periodKey, column, reporting_period || 'Quarterly')) {

                if (period_data?.[periodIndex]) {
                  const periodDataArray = this.extractPeriodDataArray(period_data[periodIndex]);

                  // Filter for this data point
                  const dpData = periodDataArray.filter((dataPoint: any) => {
                    const dpName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';
                    return dpName === dataPointName;
                  });
                  columnTotal += this.sumPeriodData(dpData, entity);
                }
              }
            });
          }

          values[columnIndex] = parseFloat(columnTotal.toFixed(4));
        });
      });

      // Convert map to rows array with aggregated values
      const rows = Array.from(dataPointMap.entries()).map(([dataPointName, values]) => {
        const totalValue = values.reduce((sum, value) => sum + value, 0);
        return {
          data_point: dataPointName,
          value: parseFloat(totalValue.toFixed(4))
        };
      });

      return {
        table_type: 'dp_breakdown',
        reporting_period: reporting_period || 'Quarterly',
        rows
      };

    } catch (error) {
      console.error('Error generating new DP breakdown:', error);
      return {
        error: `Error generating new DP breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate year-based entity breakdown table for queried_data
   * Now generates columns based on reporting_period and aggregates across all years
   */
  private async generateYearBasedEntityBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    year?: number[],
    period_data?: any[][],
    entity?: string[],
    reporting_period?: string
  ): Promise<any> {
    try {
      // Get entity details using the provided query
      const filteredLocations = await this.userProfileRepository.locationOnes(94).find({
        include: [
          {
            relation: "locationTwos",
            scope: {
              include: [{relation: "locationThrees"}],
            },
          },
        ],
      });

      const shapedSite = filteredLocations.map(item => {
        if (item.locationTwos) {
          item.locationTwos = item.locationTwos.filter(locationTwo =>
            locationTwo.locationThrees && locationTwo.locationThrees.length > 0
          );
        }
        return item;
      }).filter(item => item.locationTwos && item.locationTwos.length > 0);

      // Create entity name mapping
      const entityNameMap = new Map<string, string>();
      shapedSite.forEach(location => {
        const level1Key = `1-${location.id}`;
        entityNameMap.set(level1Key, location.name || '');

        location.locationTwos?.forEach((locationTwo: any) => {
          const level2Key = `2-${locationTwo.id}`;
          entityNameMap.set(level2Key, locationTwo.name || '');

          locationTwo.locationThrees?.forEach((locationThree: any) => {
            const level3Key = `3-${locationThree.id}`;
            entityNameMap.set(level3Key, locationThree.name || '');
          });
        });
      });

      // Generate rows for each entity with total aggregation across all periods and years
      const rows = (entity || []).map(entityKey => {
        const entityName = entityNameMap.get(entityKey) || entityKey;

        // Calculate total value across all periods and years for this entity
        let totalValue = 0;

        // Aggregate across all periods and years
        valid_periods.forEach((period, periodIndex) => {
          // Handle different period_data structures
          let periodDataArray: any[] = [];

          if (period_data?.[periodIndex]) {
            const periodItem: any = period_data[periodIndex];

            // Check if it's the fetchDataFromDataLayer structure
            if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
              periodDataArray = periodItem.data;
            } else if (Array.isArray(periodItem)) {
              // If it's already an array
              periodDataArray = periodItem;
            } else {
              // If it's a single item, wrap in array
              periodDataArray = [periodItem];
            }
          }

          // Filter data for this specific entity
          const entityData = periodDataArray.filter((dataPoint: any) => {
            const dataEntityKey = `${dataPoint.level}-${dataPoint.locationId}`;
            const itemEntityName = this.extractEntityName(dataPoint);

            // Try multiple matching approaches
            return dataEntityKey === entityKey ||
              itemEntityName === entityKey ||
              this.matchesEntityFormat(dataPoint, entityKey);
          });

          // Sum computedValue for this entity in this period
          totalValue += entityData.reduce((sum, item) => {
            // Handle different data types for computedValue
            let value = 0;
            if (item.computedValue !== null && item.computedValue !== undefined) {
              if (typeof item.computedValue === 'number') {
                value = item.computedValue;
              } else if (typeof item.computedValue === 'string') {
                // Handle common placeholder values that should be treated as 0
                const trimmedValue = item.computedValue.trim();
                if (trimmedValue === '' || trimmedValue === '-' || trimmedValue === 'N/A' || trimmedValue === 'NA') {
                  value = 0;
                } else {
                  const parsed = parseFloat(trimmedValue);
                  value = isNaN(parsed) ? 0 : parsed;
                }
              }
            }
            return sum + value;
          }, 0);
        });

        return {
          entity: entityName,
          value: parseFloat(totalValue.toFixed(4))
        };
      });

      return {
        table_type: 'entity_breakdown',
        reporting_period: reporting_period || 'Quarterly',
        rows
      };

    } catch (error) {
      console.error('Error generating year-based entity breakdown:', error);
      return {
        error: `Error generating year-based entity breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate DCF breakdown table
   */
  private async generateDcfBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    _reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    sub_data_source: string,
    _raw_parameters: any,
    indicator_parameters: any
  ): Promise<any> {
    try {
      if (sub_data_source === 'raw') {
        // Get DCF names from formCollectionRepository
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        const dcfNameMap = new Map<number, string>();
        dcfForms.forEach(form => {
          if (form.id !== undefined) {
            dcfNameMap.set(form.id, form.title || '');
          }
        });

        // Generate rows for each data point (assuming raw data has data points)
        const dataPointMap = new Map<string, number[]>();

        // Process period data to extract data points using title
        period_data.forEach((periodDataArray, periodIndex) => {
          periodDataArray.forEach((dataPoint: any) => {
            const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

            if (!dataPointMap.has(dataPointName)) {
              dataPointMap.set(dataPointName, new Array(valid_periods.length).fill(0));
            }

            const values = dataPointMap.get(dataPointName)!;
            values[periodIndex] += parseFloat(dataPoint.computedValue) || 0;
          });
        });

        const rows = Array.from(dataPointMap.entries()).map(([dataPointName, values]) => ({
          data_point: dataPointName,
          values: values.map(value => parseFloat(value.toFixed(4)))
        }));

        return {
          table_type: 'dcf_breakdown',
          dcf_name: dcfForms.length > 0 ? dcfForms[0].title : 'Unknown DCF',
          rows
        };

      } else if (sub_data_source === 'indicator') {
        // For indicator data source, show DCF breakdown
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        const dcfNameMap = new Map<number, string>();
        dcfForms.forEach(form => {
          if (form.id !== undefined) {
            dcfNameMap.set(form.id, form.title || '');
          }
        });

        // Generate rows for each DCF
        const rows = dcfIds.map(dcfId => {
          const dcfName = dcfNameMap.get(dcfId) || `DCF ${dcfId}`;

          // Calculate total value across all periods for this DCF
          let totalValue = 0;
          valid_periods.forEach((_period, periodIndex) => {
            const periodDataArray = period_data[periodIndex] || [];

            // Filter data for this specific DCF
            const dcfData = periodDataArray.filter((dataPoint: any) => dataPoint.dcfId === dcfId);

            // Sum computedValue for this DCF in this period
            totalValue += dcfData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
          });

          return {
            dcf_name: dcfName,
            value: parseFloat(totalValue.toFixed(4))
          };
        });

        // Get indicator name
        let indicatorName = 'Unknown Indicator';
        if (indicator_parameters?.indicator_name && indicator_parameters.indicator_name.length > 0) {
          try {
            const indicatorList = this.hardcodedSTTIndicatorList.filter(x => indicator_parameters?.indicator_name.includes(x.id))

            if (indicatorList.length > 0) {
              indicatorName = indicatorList[0].title || 'Unknown Indicator';
            }
          } catch (error) {
            console.error('Error getting indicator name:', error);
          }
        }

        return {
          table_type: 'dcf_breakdown',
          indicator_name: indicatorName,
          rows
        };
      }

      // Fallback
      return {
        error: 'Unsupported sub_data_source for DCF breakdown'
      };

    } catch (error) {
      console.error('Error generating DCF breakdown:', error);
      return {
        error: `Error generating DCF breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate Data Point (DP) breakdown table
   */
  private async generateDpBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    _reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    sub_data_source: string,
    _raw_parameters: any,
    indicator_parameters: any
  ): Promise<any> {
    try {
      if (sub_data_source === 'raw') {
        // Get DCF names from formCollectionRepository for context
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        // Generate rows for each data point using title
        const dataPointMap = new Map<string, number[]>();

        // Process period data to extract data points using title
        period_data.forEach((periodDataArray, periodIndex) => {
          periodDataArray.forEach((dataPoint: any) => {
            const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

            if (!dataPointMap.has(dataPointName)) {
              dataPointMap.set(dataPointName, new Array(valid_periods.length).fill(0));
            }

            const values = dataPointMap.get(dataPointName)!;
            values[periodIndex] += parseFloat(dataPoint.computedValue) || 0;
          });
        });

        const rows = Array.from(dataPointMap.entries()).map(([dataPointName, values]) => {
          const totalValue = values.reduce((sum, value) => sum + value, 0);
          return {
            data_point: dataPointName,
            value: parseFloat(totalValue.toFixed(4))
          };
        });

        return {
          table_type: 'dp_breakdown',
          dcf_name: dcfForms.length > 0 ? dcfForms[0].title : 'Unknown DCF',
          rows
        };

      } else if (sub_data_source === 'indicator') {
        // For indicator data source, show data point breakdown
        const dataPointMap = new Map<string, number[]>();

        // Process period data to extract data points using title
        period_data.forEach((periodDataArray, periodIndex) => {
          periodDataArray.forEach((dataPoint: any) => {
            const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

            if (!dataPointMap.has(dataPointName)) {
              dataPointMap.set(dataPointName, new Array(valid_periods.length).fill(0));
            }

            const values = dataPointMap.get(dataPointName)!;
            values[periodIndex] += parseFloat(dataPoint.computedValue) || 0;
          });
        });

        const rows = Array.from(dataPointMap.entries()).map(([dataPointName, values]) => {
          const totalValue = values.reduce((sum, value) => sum + value, 0);
          return {
            data_point: dataPointName,
            value: parseFloat(totalValue.toFixed(4))
          };
        });

        // Get indicator name
        let indicatorName = 'Unknown Indicator';
        if (indicator_parameters?.indicator_name && indicator_parameters.indicator_name.length > 0) {
          try {
            const indicatorList = this.hardcodedSTTIndicatorList.filter(x => indicator_parameters?.indicator_name.includes(x.id))

            if (indicatorList.length > 0) {
              indicatorName = indicatorList[0].title || 'Unknown Indicator';
            }
          } catch (error) {
            console.error('Error getting indicator name:', error);
          }
        }

        return {
          table_type: 'dp_breakdown',
          indicator_name: indicatorName,
          rows
        };
      }

      // Fallback
      return {
        error: 'Unsupported sub_data_source for DP breakdown'
      };

    } catch (error) {
      console.error('Error generating DP breakdown:', error);
      return {
        error: `Error generating DP breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate year-based DCF breakdown table for queried_data
   */
  private async generateYearBasedDcfBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    year?: number[],
    period_data?: any[][],
    dcfIds?: number[],
    sub_data_source?: string,
    _raw_parameters?: any,
    indicator_parameters?: any
  ): Promise<any> {
    try {
      // For queried_data, use year-based column headers
      const columns = year ? year.map(y => y.toString()) : ['Year'];

      if (sub_data_source === 'raw' && dcfIds) {
        // Get DCF names from formCollectionRepository
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        const dcfNameMap = new Map<number, string>();
        dcfForms.forEach(form => {
          if (form.id !== undefined) {
            dcfNameMap.set(form.id, form.title || '');
          }
        });

        // For queried_data with raw data source, show DCF breakdown (not data point breakdown)
        // Generate rows for each DCF
        const rows = dcfIds.map(dcfId => {
          const dcfName = dcfNameMap.get(dcfId) || `DCF ${dcfId}`;

          // Calculate values for each year for this DCF
          const values = columns.map((_column, columnIndex) => {
            const targetYear = year ? year[columnIndex] : null;
            let yearTotal = 0;

            // For queried_data, aggregate data across all periods that belong to this year
            if (targetYear) {
              valid_periods.forEach((period, periodIndex) => {
                if (this.periodBelongsToYear(period, targetYear)) {
                  // Handle different period_data structures
                  let periodDataArray: any[] = [];

                  if (period_data?.[periodIndex]) {
                    const periodItem: any = period_data[periodIndex];

                    if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                      periodDataArray = periodItem.data;
                    } else if (Array.isArray(periodItem)) {
                      periodDataArray = periodItem;
                    } else {
                      periodDataArray = [periodItem];
                    }
                  }

                  // Filter data for this specific DCF
                  const dcfData = periodDataArray.filter((dataPoint: any) => dataPoint.dcfId === dcfId);

                  // Sum computedValue for this DCF in this period
                  yearTotal += dcfData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
                }
              });
            } else {
              // Fallback: if no year provided, aggregate all data for this DCF
              valid_periods.forEach((_period, periodIndex) => {
                let periodDataArray: any[] = [];

                if (period_data?.[periodIndex]) {
                  const periodItem: any = period_data[periodIndex];

                  if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                    periodDataArray = periodItem.data;
                  } else if (Array.isArray(periodItem)) {
                    periodDataArray = periodItem;
                  } else {
                    periodDataArray = [periodItem];
                  }
                }

                const dcfData = periodDataArray.filter((dataPoint: any) => dataPoint.dcfId === dcfId);
                yearTotal += dcfData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
              });
            }

            return yearTotal;
          });

          return {
            dcf_name: dcfName,
            values: values.map(value => parseFloat(value.toFixed(4)))
          };
        });

        return {
          table_type: 'dcf_breakdown',
          reporting_period: 'year-based',
          rows
        };

      } else if (sub_data_source === 'indicator' && dcfIds) {
        // For indicator data source, show DCF breakdown
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        const dcfNameMap = new Map<number, string>();
        dcfForms.forEach(form => {
          if (form.id !== undefined) {
            dcfNameMap.set(form.id, form.title || '');
          }
        });

        // Generate rows for each DCF
        const rows = dcfIds.map(dcfId => {
          const dcfName = dcfNameMap.get(dcfId) || `DCF ${dcfId}`;

          // Calculate values for each year for this DCF
          const values = columns.map((_column, columnIndex) => {
            const targetYear = year ? year[columnIndex] : null;
            let yearTotal = 0;

            // For queried_data, aggregate data across all periods that belong to this year
            if (targetYear) {
              valid_periods.forEach((period, periodIndex) => {
                if (this.periodBelongsToYear(period, targetYear)) {
                  // Handle different period_data structures
                  let periodDataArray: any[] = [];

                  if (period_data?.[periodIndex]) {
                    const periodItem: any = period_data[periodIndex];

                    if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                      periodDataArray = periodItem.data;
                    } else if (Array.isArray(periodItem)) {
                      periodDataArray = periodItem;
                    } else {
                      periodDataArray = [periodItem];
                    }
                  }

                  // Filter data for this specific DCF
                  const dcfData = periodDataArray.filter((dataPoint: any) => dataPoint.dcfId === dcfId);

                  // Sum computedValue for this DCF in this period
                  yearTotal += dcfData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
                }
              });
            } else {
              // Fallback: if no year provided, aggregate all data for this DCF
              valid_periods.forEach((period, periodIndex) => {
                let periodDataArray: any[] = [];

                if (period_data?.[periodIndex]) {
                  const periodItem: any = period_data[periodIndex];

                  if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                    periodDataArray = periodItem.data;
                  } else if (Array.isArray(periodItem)) {
                    periodDataArray = periodItem;
                  } else {
                    periodDataArray = [periodItem];
                  }
                }

                const dcfData = periodDataArray.filter((dataPoint: any) => dataPoint.dcfId === dcfId);
                yearTotal += dcfData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
              });
            }

            return yearTotal;
          });

          return {
            dcf_name: dcfName,
            values: values.map(value => parseFloat(value.toFixed(4)))
          };
        });

        // Get indicator name
        let indicatorName = 'Unknown Indicator';
        if (indicator_parameters?.indicator_name && indicator_parameters.indicator_name.length > 0) {
          try {
            const indicatorList = this.hardcodedSTTIndicatorList.filter(x => indicator_parameters?.indicator_name.includes(x.id))

            if (indicatorList.length > 0) {
              indicatorName = indicatorList[0].title || 'Unknown Indicator';
            }
          } catch (error) {
            console.error('Error getting indicator name:', error);
          }
        }

        return {
          table_type: 'dcf_breakdown',
          indicator_name: indicatorName,
          reporting_period: 'year-based',
          rows
        };
      }

      // Fallback
      return {
        error: 'Unsupported sub_data_source for year-based DCF breakdown'
      };

    } catch (error) {
      console.error('Error generating year-based DCF breakdown:', error);
      return {
        error: `Error generating year-based DCF breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate year-based Data Point (DP) breakdown table for queried_data
   */
  private async generateYearBasedDpBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    year?: number[],
    period_data?: any[][],
    dcfIds?: number[],
    sub_data_source?: string,
    _raw_parameters?: any,
    indicator_parameters?: any
  ): Promise<any> {
    try {
      // For queried_data, use year-based column headers
      const columns = year ? year.map(y => y.toString()) : ['Year'];

      if (sub_data_source === 'raw' && dcfIds) {
        // Get DCF names from formCollectionRepository for context
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        // For queried_data with raw data source, show data point breakdown by title
        const dataPointMap = new Map<string, number[]>();

        // Process period data to extract data points using title
        columns.forEach((_column, columnIndex) => {
          const targetYear = year ? year[columnIndex] : null;

          // For queried_data, aggregate data across all periods that belong to this year
          if (targetYear) {
            valid_periods.forEach((period, periodIndex) => {
              if (this.periodBelongsToYear(period, targetYear)) {
                // Handle different period_data structures
                let periodDataArray: any[] = [];

                if (period_data?.[periodIndex]) {
                  const periodItem: any = period_data[periodIndex];

                  if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                    periodDataArray = periodItem.data;
                  } else if (Array.isArray(periodItem)) {
                    periodDataArray = periodItem;
                  } else {
                    periodDataArray = [periodItem];
                  }
                }

                // Extract data points by title
                periodDataArray.forEach((dataPoint: any) => {
                  const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

                  if (!dataPointMap.has(dataPointName)) {
                    dataPointMap.set(dataPointName, new Array(columns.length).fill(0));
                  }

                  const values = dataPointMap.get(dataPointName)!;
                  values[columnIndex] += parseFloat(dataPoint.computedValue) || 0;
                });
              }
            });
          } else {
            // Fallback: if no year provided, aggregate all data
            valid_periods.forEach((_period, periodIndex) => {
              let periodDataArray: any[] = [];

              if (period_data?.[periodIndex]) {
                const periodItem: any = period_data[periodIndex];

                if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                  periodDataArray = periodItem.data;
                } else if (Array.isArray(periodItem)) {
                  periodDataArray = periodItem;
                } else {
                  periodDataArray = [periodItem];
                }
              }

              periodDataArray.forEach((dataPoint: any) => {
                const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

                if (!dataPointMap.has(dataPointName)) {
                  dataPointMap.set(dataPointName, new Array(columns.length).fill(0));
                }

                const values = dataPointMap.get(dataPointName)!;
                values[columnIndex] += parseFloat(dataPoint.computedValue) || 0;
              });
            });
          }
        });

        const rows = Array.from(dataPointMap.entries()).map(([dataPointName, values]) => ({
          data_point: dataPointName,
          values: values.map(value => parseFloat(value.toFixed(4)))
        }));

        return {
          table_type: 'dp_breakdown',
          dcf_name: dcfForms.length > 0 ? dcfForms[0].title : 'Unknown DCF',
          reporting_period: 'year-based',
          rows
        };

      } else if (sub_data_source === 'indicator' && dcfIds) {
        // For indicator data source, show data point breakdown
        const dataPointMap = new Map<string, number[]>();

        // Process period data to extract data points using title
        columns.forEach((_column, columnIndex) => {
          const targetYear = year ? year[columnIndex] : null;

          // For queried_data, aggregate data across all periods that belong to this year
          if (targetYear) {
            valid_periods.forEach((period, periodIndex) => {
              if (this.periodBelongsToYear(period, targetYear)) {
                // Handle different period_data structures
                let periodDataArray: any[] = [];

                if (period_data?.[periodIndex]) {
                  const periodItem: any = period_data[periodIndex];

                  if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                    periodDataArray = periodItem.data;
                  } else if (Array.isArray(periodItem)) {
                    periodDataArray = periodItem;
                  } else {
                    periodDataArray = [periodItem];
                  }
                }

                // Extract data points by title
                periodDataArray.forEach((dataPoint: any) => {
                  const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

                  if (!dataPointMap.has(dataPointName)) {
                    dataPointMap.set(dataPointName, new Array(columns.length).fill(0));
                  }

                  const values = dataPointMap.get(dataPointName)!;
                  values[columnIndex] += parseFloat(dataPoint.computedValue) || 0;
                });
              }
            });
          } else {
            // Fallback: if no year provided, aggregate all data
            valid_periods.forEach((_period, periodIndex) => {
              let periodDataArray: any[] = [];

              if (period_data?.[periodIndex]) {
                const periodItem: any = period_data[periodIndex];

                if (periodItem && typeof periodItem === 'object' && periodItem.data && Array.isArray(periodItem.data)) {
                  periodDataArray = periodItem.data;
                } else if (Array.isArray(periodItem)) {
                  periodDataArray = periodItem;
                } else {
                  periodDataArray = [periodItem];
                }
              }

              periodDataArray.forEach((dataPoint: any) => {
                const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

                if (!dataPointMap.has(dataPointName)) {
                  dataPointMap.set(dataPointName, new Array(columns.length).fill(0));
                }

                const values = dataPointMap.get(dataPointName)!;
                values[columnIndex] += parseFloat(dataPoint.computedValue) || 0;
              });
            });
          }
        });

        const rows = Array.from(dataPointMap.entries()).map(([dataPointName, values]) => ({
          data_point: dataPointName,
          values: values.map(value => parseFloat(value.toFixed(4)))
        }));

        // Get indicator name
        let indicatorName = 'Unknown Indicator';
        if (indicator_parameters?.indicator_name && indicator_parameters.indicator_name.length > 0) {
          try {
            const indicatorList = this.hardcodedSTTIndicatorList.filter(x => indicator_parameters?.indicator_name.includes(x.id))

            if (indicatorList.length > 0) {
              indicatorName = indicatorList[0].title || 'Unknown Indicator';
            }
          } catch (error) {
            console.error('Error getting indicator name:', error);
          }
        }

        return {
          table_type: 'dp_breakdown',
          indicator_name: indicatorName,
          reporting_period: 'year-based',
          rows
        };
      }

      // Fallback
      return {
        error: 'Unsupported sub_data_source for year-based DP breakdown'
      };

    } catch (error) {
      console.error('Error generating year-based DP breakdown:', error);
      return {
        error: `Error generating year-based DP breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Format period for display in table columns and chart x-axis
   */
  private formatPeriodForDisplay(period: string): string {
    // Handle different period formats - show only period part without year
    if (period.includes('-') && period.match(/^\d{2}-\d{4}$/)) {
      // Monthly format: "01-2025" -> "Jan"
      const [month] = period.split('-');
      return this.getMonthName(parseInt(month));
    } else if (period.includes('Q')) {
      // Quarterly format: "Q1-2025" -> "Q1"
      const [quarter] = period.split('-');
      return quarter;
    } else if (period.includes('H')) {
      // Half-yearly format: "H1-2025" -> "H1"
      const [half] = period.split('-');
      return half;
    } else if (period.includes('BM')) {
      // Bi-monthly format: "BM1-2025" -> "BM1"
      const [bimonth] = period.split('-');
      return bimonth;
    } else if (period.match(/^\d{4}$/)) {
      // Yearly format: "2025" -> "2025"
      return period;
    }

    // Default fallback
    return period;
  }

}
