# Custom Period Generation with Frequency Validation

## Overview

The enhanced custom period functionality now generates `valid_periods` based on `reporting_frequency`, `reporting_period_from`, and `reporting_period_to` with comprehensive validation.

## Required Fields for Custom Periods

When `reporting_period` is "Custom", the following fields are **required**:
- `reporting_frequency`: One of ["monthly", "quarterly", "bi-monthly", "half-yearly"]
- `reporting_period_from`: Start period in "MMM-YYYY" format (e.g., "Jan-2024")
- `reporting_period_to`: End period in "MMM-YYYY" format (e.g., "Apr-2024")

## Validation Rules

### 1. **Linear Progression**
- `reporting_period_from` must be earlier than or equal to `reporting_period_to`
- ✅ Valid: "Jan-2024" to "Mar-2025"
- ❌ Invalid: "Mar-2024" to "Apr-2024" (backwards)

### 2. **Frequency Alignment**

#### **Monthly**
- No additional restrictions
- Any valid month range is allowed

#### **Quarterly**
- Must start at quarter beginning: Jan, Apr, Jul, Oct
- Must end at quarter end: Mar, Jun, Sep, Dec
- Period span must be multiple of 3 months

#### **Bi-Monthly**
- Must start at bi-monthly beginning: Jan, Mar, May, Jul, Sep, Nov
- Must end at bi-monthly end: Feb, Apr, Jun, Aug, Oct, Dec
- Period span must be multiple of 2 months

#### **Half-Yearly**
- Must start at half-year beginning: Jan, Jul
- Must end at half-year end: Jun, Dec
- Period span must be multiple of 6 months

## Examples

### ✅ Valid Examples

#### 1. Monthly Frequency
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Monthly",
  "reporting_period_from": "Jan-2024",
  "reporting_period_to": "Apr-2024"
}
```
**Output:** `["Jan-2024", "Feb-2024", "Mar-2024", "Apr-2024"]`

#### 2. Quarterly Frequency
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Quarterly",
  "reporting_period_from": "Jan-2024",
  "reporting_period_to": "Dec-2024"
}
```
**Output:** `["Q1-2024", "Q2-2024", "Q3-2024", "Q4-2024"]`

#### 3. Bi-Monthly Frequency
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Bi-Monthly",
  "reporting_period_from": "Jan-2024",
  "reporting_period_to": "Jun-2024"
}
```
**Output:** `["BM1-2024", "BM2-2024", "BM3-2024"]`

#### 4. Half-Yearly Frequency
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Half-Yearly",
  "reporting_period_from": "Jan-2024",
  "reporting_period_to": "Dec-2024"
}
```
**Output:** `["H1-2024", "H2-2024"]`

### ❌ Invalid Examples with Error Messages

#### 1. Missing Required Fields
```json
{
  "reporting_period": "Custom",
  "reporting_period_from": "Jan-2024"
  // Missing reporting_period_to and reporting_frequency
}
```
**Error:** `"reporting_period_to is required when reporting_period is 'Custom'"`

#### 2. Invalid Frequency
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Weekly",
  "reporting_period_from": "Jan-2024",
  "reporting_period_to": "Apr-2024"
}
```
**Error:** `"Invalid reporting_frequency 'Weekly'. Allowed values: monthly, quarterly, bi-monthly, half-yearly"`

#### 3. Quarterly Misalignment
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Quarterly",
  "reporting_period_from": "Feb-2024",
  "reporting_period_to": "May-2024"
}
```
**Error:** `"For quarterly frequency, reporting_period_from must start at quarter beginning (Jan, Apr, Jul, Oct). Got: Feb-2024"`

#### 4. Bi-Monthly Span Error
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Bi-Monthly",
  "reporting_period_from": "Jan-2024",
  "reporting_period_to": "Apr-2024"
}
```
**Error:** `"For bi-monthly frequency, period span must be multiple of 2 months. Current span: 4 months"`

#### 5. Half-Yearly Boundary Error
```json
{
  "reporting_period": "Custom",
  "reporting_frequency": "Half-Yearly",
  "reporting_period_from": "Mar-2024",
  "reporting_period_to": "Aug-2024"
}
```
**Error:** `"For half-yearly frequency, reporting_period_from must start at half-year beginning (Jan or Jul). Got: Mar-2024"`

## Period Format Support

The system supports both formats for input:
- **Month Names:** "Jan-2024", "Feb-2024", etc.
- **Numeric:** "01-2024", "02-2024", etc.

Output is always in month name format for consistency.

## Key Benefits

1. **Frequency-Based Generation:** Periods match the specified reporting frequency
2. **Comprehensive Validation:** Prevents invalid period configurations
3. **Clear Error Messages:** Specific guidance on what's wrong and how to fix it
4. **Flexible Input:** Supports both month name and numeric formats
5. **Consistent Output:** Standardized period format across all frequencies
